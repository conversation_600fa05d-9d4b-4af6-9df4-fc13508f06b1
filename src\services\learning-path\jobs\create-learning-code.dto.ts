import { PrismaService } from 'src/services/prisma/prisma.service';
import { CCreateLearningCodeBodyDto } from '../dto/create-learning-code.dto';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { HttpStatus } from '@nestjs/common';

export const JCreateLearningCode = async (
  prisma: PrismaService,
  body: CCreateLearningCodeBodyDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const isAlreadyExists = await prisma.learning_path.findFirst({
      where: {
        OR: [{ is_deleted: false }, { is_deleted: null }],
        AND: [
          {
            OR: [
              {
                code: body.learning_code,
              },
              {
                name: body.learning_code_name,
              },
            ],
          },
        ],
      },
      select: {
        code: true,
        name: true,
      },
    });

    if (isAlreadyExists) {
      throw handleError({
        status: HttpStatus.BAD_REQUEST,
        message:
          isAlreadyExists.code === body.learning_code
            ? 'learning code is exist'
            : 'learning code name is exist',
      });
    }

    await prisma.learning_path.create({
      data: {
        code: body.learning_code,
        name: body.learning_code_name,
        learning_job: {
          createMany: {
            data: body.job_name_id.map((id) => ({
              job_name_id: id,
              status: 'active',
            })),
          },
        },
      },
    });

    return {
      status: true,
      message: 'success insert learning code',
      data: null,
    };
  } catch (error) {
    throw handleError(error);
  }
};
