import { PrismaService } from 'src/services/prisma/prisma.service';
import { materials_repository, Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { handleError } from 'src/common/utils/error.util';
import {
  CGetManageMaterialListQueryDto,
  IGetManageMaterialListResponseDto,
} from '../dto/list.dto';

export const JGetManageMaterialList = async (
  prisma: PrismaService,
  query: CGetManageMaterialListQueryDto,
): Promise<IGlobalResponseDto<IGetManageMaterialListResponseDto[]>> => {
  try {
    const {
      search,
      search_by,
      page = 1,
      limit = 10,
      type,
      category_id,
      feature,
      level_id,
    } = query;

    const filter: Prisma.materials_repositoryWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
      feature,
      type,
      ...(category_id
        ? {
            material_category_mapping: {
              some: { category_id },
            },
          }
        : {}),
      ...(level_id
        ? {
            material_level_mapping: {
              some: { levels_id: level_id },
            },
          }
        : {}),
    };

    if (search && search_by && search_by !== 'associated') {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (search && search_by && search_by === 'associated') {
      filter.AND = [
        {
          sub_section: {
            some: { name: { contains: search, mode: 'insensitive' } },
          },
        },
      ];
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.materials_repository.findMany({
        where: filter,
        include: {
          material_level_mapping: {
            include: {
              levels: true,
            },
          },
          material_category_mapping: {
            include: {
              category: true,
            },
          },
          sub_section: true,
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
        skip,
        take,
      }),
      prisma.materials_repository.count({ where: filter }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetManageMaterialListResponseDto[] = mapMaterialsToDto(data);

    return {
      status: true,
      message: 'success get data material',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/learning/material/list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

export function mapMaterialsToDto(
  data: (materials_repository & {
    material_level_mapping: {
      levels: { ID: number; name: string | null } | null;
    }[];
    material_category_mapping: {
      category: { id: number; category_name: string | null } | null;
    }[];
    sub_section: { ID: number; name: string | null }[];
  })[],
): IGetManageMaterialListResponseDto[] {
  return data.map((item) => ({
    id: item.id,
    type: item.type,
    name: item.name,
    file_format: item.file_format,
    levels: item.material_level_mapping
      .filter((ml) => ml.levels)
      .map((ml) => ({
        id: ml.levels?.ID ?? null,
        level: ml.levels?.name ?? null,
      })),
    categories: item.material_category_mapping
      .filter((mc) => mc.category)
      .map((mc) => ({
        id: mc.category?.id ?? null,
        name: mc.category?.category_name ?? null,
      })),
    associated: item.sub_section.map((ss) => ({
      id: ss.ID ?? null,
      name: ss.name ?? null,
    })),
    link: item.link,
    filesize: item.filesize,
    feature: item.feature,
    created_at: item.created_at ? item.created_at.toISOString() : null,
    created_by: item.created_by,
    updated_by: item.updated_by,
    last_updated: item.last_updated ? item.last_updated.toISOString() : null,
  }));
}
