import { createZodDto } from 'nestjs-zod';
import z from 'zod';

export const updateUserBodySchema = z.object({
  npk: z.string().optional(),
  email: z.string().optional(),
  second_email: z.string().optional(),
  point: z.number().nullable().optional(),
  forum_title: z.string().optional(),
  name: z.string().optional(),
  job_name: z.string().optional(),
  password: z.string().optional(),
  job_name_id: z.number().optional(),
  phone_number: z.string().optional(),
  user_type_id: z.number().optional(),
  user_role_id: z.number().optional(),
  supervisor_id: z.number().optional(),
  supervisor_name: z.string().optional(),
  supervisor_npk: z.string().optional(),
  is_active: z.boolean().optional(),
  is_need_neop: z.boolean().optional(),
  is_new_user: z.boolean().optional(),
  is_deleted: z.boolean().optional(),
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
  forum_title_id: z.number().optional(),
  entity_id: z.number().optional(),
  is_need_welcoming_kit: z.boolean().optional(),
});

export class CUpdateUserBodyDto extends createZodDto(updateUserBodySchema) {}

const updateUserParamsSchema = z.object({ id: z.coerce.number() });
export class CUpdateUserParamsDto extends createZodDto(
  updateUserParamsSchema,
) {}
