import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  convertImageToWebp,
  removeExtension,
} from 'src/common/utils/image-processor.util';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import { CUpdateFAQBodyDto, CUpdateFAQParamsDto } from '../dto/update.dto';
import { HttpStatus } from '@nestjs/common';
import dayjs from 'dayjs';

export const JUpdateFAQ = async (
  prisma: PrismaService,
  params: CUpdateFAQParamsDto,
  body: CUpdateFAQBodyDto,
  file: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const data = body.data;

    await prisma.faq
      .findUniqueOrThrow({ where: { ID: params.id } })
      .catch(() => {
        throw handleError({
          message: 'FAQ not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const { faq } = await prisma.$transaction(async (tx) => {
      const faq = await tx.faq.update({
        where: { ID: params.id },
        data: {
          question: data.question,
          answer: data.answer,
          updated_at: new Date(),
          is_deleted: data.is_deleted,
        },
        select: { ID: true },
      });

      await tx.faq_tags.deleteMany({ where: { faq_id: faq.ID } });

      await tx.faq_tags.createMany({
        data: data.tags.map((it: any) => ({ tags_id: +it, faq_id: faq.ID })),
      });

      return { faq };
    });

    if (file && file.mimetype.startsWith('image/')) {
      const pathName = await uploadFAQImage(file);

      await prisma.faq.update({
        where: { ID: faq.ID },
        data: {
          imgFaq: pathName,
        },
      });
    }

    return {
      status: true,
      message: 'success update data',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

async function uploadFAQImage(file: Express.Multer.File) {
  let buffer = file.buffer;
  let filePathName = file.originalname;
  const mimetype = 'image/webp';

  buffer = await convertImageToWebp(buffer);
  filePathName = `faq/${dayjs().unix()}-${removeExtension(filePathName)}.webp`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': mimetype,
  });

  return `lemon/${filePathName}`;
}
