import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { FormDataGuard } from 'src/common/guards/form-data.guard';
import { FileUploadInterceptor } from 'src/common/interceptors/file-upload.interceptor';
import { ImageRepositoryService } from './image-repository.service';
import { CCreateImageRepositoryBodyDto } from './dto/create.dto';
import { CGetImageRepositoryListQueryDto } from './dto/list.dto';
import { CGetImageRepositoryDetailParamsDto } from './dto/detail.dto';
import {
  CUpdateImageRepositoryBodyDto,
  CUpdateImageRepositoryParamsDto,
} from './dto/update.dto';

@Controller('/admin/learning')
export class ImageRepositoryController {
  constructor(
    private readonly imageRepositoryService: ImageRepositoryService,
  ) {}

  // Auth
  @Get('img-repository/list')
  getImageRepositoryList(@Query() query: CGetImageRepositoryListQueryDto) {
    return this.imageRepositoryService.getImageRepositoryList(query);
  }

  //   Auth
  @Get('img-repository/detail/:id')
  getImageRepositoryDetail(
    @Param() params: CGetImageRepositoryDetailParamsDto,
  ) {
    return this.imageRepositoryService.getImageRepositoryDetail(params);
  }

  // Auth
  @Post('img-repository/insert')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    allowedMimeTypes: /^image\/(jpg|jpeg|png|webp|svg)$/,
    maxSizeMB: 5,
  })
  async createImageRepository(
    @Body() body: CCreateImageRepositoryBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.imageRepositoryService.createImageRepository(body, file);
  }

  // Auth
  @Post('img-repository/update/:id')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    allowedMimeTypes: /^image\/(jpg|jpeg|png|webp|svg)$/,
    maxSizeMB: 5,
  })
  async updateImageRepository(
    @Param() params: CUpdateImageRepositoryParamsDto,
    @Body() body: CUpdateImageRepositoryBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.imageRepositoryService.updateImageRepository(
      params,
      body,
      file,
    );
  }
}
