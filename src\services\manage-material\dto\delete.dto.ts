import { createZodDto } from 'nestjs-zod';
import { zBoolean } from 'src/common/utils/validation.util';
import z from 'zod';

const deleteMaterialSchema = z.object({ is_deleted: zBoolean() });

export class CDeleteMaterialBodyDto extends createZodDto(
  deleteMaterialSchema,
) {}

const deleteMaterialParams = z.object({ id: z.coerce.number() });

export class CDeleteMaterialParamsDto extends createZodDto(
  deleteMaterialParams,
) {}
