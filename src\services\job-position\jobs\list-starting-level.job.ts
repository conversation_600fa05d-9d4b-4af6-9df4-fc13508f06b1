import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetStartingLevelListQueryDto,
  IGetStartingLevelListResponseDto,
} from '../dto/list-starting-level.dto';
import { Prisma } from '@prisma/client';

export const JGetStartingLevelList = async (
  prisma: PrismaService,
  query: CGetStartingLevelListQueryDto,
): Promise<IGlobalResponseDto<IGetStartingLevelListResponseDto[]>> => {
  try {
    const { order, order_by } = query;

    let orderBy: Prisma.levelsOrderByWithAggregationInput = {
      ID: 'asc',
    };

    if (order && order_by) {
      orderBy = {
        [order_by]: {
          sort: order,
          nulls: 'last',
        },
      };
    }

    const data = await prisma.levels.findMany({
      where: {
        status: 'active',
        OR: [{ is_deleted: false }, { is_deleted: null }],
      },
      select: {
        ID: true,
        name: true,
        level: true,
      },
      orderBy,
    });

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetStartingLevelListResponseDto[] = data.map((item) => ({
      id: item.ID,
      name: item.name,
      level: item.level,
    }));

    return {
      status: true,
      message: 'success get data level',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
