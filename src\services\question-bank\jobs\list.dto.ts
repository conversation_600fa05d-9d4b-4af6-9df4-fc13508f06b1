import { PrismaService } from 'src/services/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { handleError } from 'src/common/utils/error.util';
import {
  CGetQuestionBankListQueryDto,
  IGetQuestionBankListResponseDto,
} from '../dto/list.dto';

export const JGetQuestionBankList = async (
  prisma: PrismaService,
  query: CGetQuestionBankListQueryDto,
): Promise<IGlobalResponseDto<IGetQuestionBankListResponseDto[]>> => {
  try {
    const {
      search,
      search_by,
      page = 1,
      limit = 10,
      category_id,
      feature,
      level_id,
      is_image,
      question_type,
    } = query;

    const filter: Prisma.questionWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
      feature,
      type: question_type,
      ...(category_id
        ? {
            question_category_mapping: {
              some: { category_id },
            },
          }
        : {}),
      ...(level_id
        ? {
            question_level_mapping: {
              some: { levels_id: level_id },
            },
          }
        : {}),
    };

    if (is_image === 'null') {
      filter.image_id = null;
    }

    if (is_image === 'notnull') {
      filter.image_id = { not: null };
    }

    if (search && search_by && search_by.startsWith('option_')) {
      filter.AND = [
        {
          [search_by.replace('option_', '')]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    } else if (search && search_by && search_by === 'question_id') {
      filter.AND = [
        {
          id: +search,
        },
      ];
    } else if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.question.findMany({
        where: filter,
        include: {
          question_level_mapping: {
            include: {
              levels: true,
            },
          },
          question_category_mapping: {
            include: {
              category: true,
            },
          },
          test_question_mapping: {
            include: {
              sub_section: true,
            },
          },
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
        skip,
        take,
      }),
      prisma.question.count({ where: filter }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetQuestionBankListResponseDto[] = data.map((item) => ({
      id: item.id,
      question: item.question,
      type: item.type,
      option_a: item.a,
      option_b: item.b,
      option_c: item.c,
      option_d: item.d,
      correct_answer: item.correct_answer,
      correct_answer_percentage: item?.correct_answer_percentage
        ? Number(item.correct_answer_percentage)
        : null,
      levels: item.question_level_mapping
        .filter((ml) => ml.levels)
        .map((ml) => ({
          id: ml.levels?.ID ?? null,
          level: ml.levels?.name ?? null,
        })),
      categories: item.question_category_mapping
        .filter((mc) => mc.category)
        .map((mc) => ({
          id: mc.category?.id ?? null,
          name: mc.category?.category_name ?? null,
        })),
      associated: item.test_question_mapping.map((ss) => ({
        section_id: ss.sub_section?.ID ?? null,
        section_name: ss.sub_section?.name ?? null,
      })),
      feature: item.feature,
      created_at: item.created_at ? item.created_at.toISOString() : null,
      created_by: item.created_by,
      updated_by: item.updated_by,
      last_updated: item.last_updated ? item.last_updated.toISOString() : null,
    }));

    return {
      status: true,
      message: 'success get question bank',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/learning/question-bank/list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
