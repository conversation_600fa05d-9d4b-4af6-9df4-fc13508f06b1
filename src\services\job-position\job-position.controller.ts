import { Body, Controller, Get, Param, Post, Query, Res } from '@nestjs/common';
import { JobPositionService } from './job-position.service';
import { CCreateJobPositionDto } from './dto/create.dto';
import {
  CUpdateJobPositionDto,
  CUpdateJobPositionParamsDto,
} from './dto/update.dto';
import { CGetJobPositionListQueryDto } from './dto/list.dto';
import { CGetJobPositionDetailParamsDto } from './dto/detail.dto';
import { CGetJobFunctionListQueryDto } from './dto/list-function.dto';
import { CGetDepartmentListQueryDto } from './dto/list-department.dto';
import { CGetStartingLevelListQueryDto } from './dto/list-starting-level.dto';
import { CExportJobPositionQueryDto } from './dto/export.dto';
import * as express from 'express';
import dayjs from 'dayjs';

@Controller('/admin')
export class JobPositionController {
  constructor(private readonly jobPositionService: JobPositionService) {}

  // Auth
  @Get('job-list')
  getJobPositionList(@Query() query: CGetJobPositionListQueryDto) {
    return this.jobPositionService.getJobPositionList(query);
  }

  // Auth
  @Get('job-detail/:id')
  getJobPositionDetail(@Param() params: CGetJobPositionDetailParamsDto) {
    return this.jobPositionService.getJobPositionDetail(params);
  }

  // Auth
  @Post('job-insert')
  async createJobPosition(@Body() body: CCreateJobPositionDto) {
    return this.jobPositionService.createJobPosition(body);
  }

  // Auth
  @Post('job-update/:id')
  async updateJobPosition(
    @Param() params: CUpdateJobPositionParamsDto,
    @Body() body: CUpdateJobPositionDto,
  ) {
    return this.jobPositionService.updateJobPosition(params, body);
  }

  // Auth
  @Get('job-export')
  async getExportJobPosition(
    @Query() query: CExportJobPositionQueryDto,
    @Res() res: express.Response,
  ) {
    const response = await this.jobPositionService.getExportJobPosition(query);

    const date = dayjs().format('DD-MM-YYYY');

    return res
      .set(
        'Content-Disposition',
        `attachment; filename=lemon_job_position_${date}.xlsx`,
      )
      .send(response);
  }

  // No Auth
  @Get('master/job-function')
  getJobFunctionList(@Query() query: CGetJobFunctionListQueryDto) {
    return this.jobPositionService.getJobFunctionList(query);
  }

  // No Auth
  @Get('master/department')
  getDepartmentList(@Query() query: CGetDepartmentListQueryDto) {
    return this.jobPositionService.getDepartmentList(query);
  }

  // No Auth
  @Get('master/starting-level')
  getStartingLevelList(@Query() query: CGetStartingLevelListQueryDto) {
    return this.jobPositionService.getStartingLevelList(query);
  }
}
