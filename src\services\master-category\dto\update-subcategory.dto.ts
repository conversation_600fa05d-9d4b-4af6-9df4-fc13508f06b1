import { createZodDto } from 'nestjs-zod';
import { zBooleanOptional } from 'src/common/utils/validation.util';
import z from 'zod';

const updateSubCategorySchema = z.object({
  subcategory_name: z.string().min(1, 'Sub category name cannot be empty'),
  category_id: z.coerce.number().optional(),
  is_deleted: zBooleanOptional(),
});

const updateSubCategoryParams = z.object({ id: z.coerce.number() });

export class CUpdateSubCategoryParamsDto extends createZodDto(
  updateSubCategoryParams,
) {}

export class CUpdateSubCategoryDto extends createZodDto(
  updateSubCategorySchema,
) {}
