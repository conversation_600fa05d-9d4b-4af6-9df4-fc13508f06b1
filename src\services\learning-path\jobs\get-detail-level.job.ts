import { handleError } from 'src/common/utils/error.util';
import {
  CGetLearningLevelDetailParamsDto,
  IGetLearningLevelDetailResponseDto,
} from '../dto/get-learning-level.dto';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetLearningLevelDetail = async (
  prisma: PrismaService,
  params: CGetLearningLevelDetailParamsDto,
): Promise<IGlobalResponseDto<IGetLearningLevelDetailResponseDto | null>> => {
  try {
    const { id } = params;

    const data = await prisma.levels.findFirst({
      where: {
        is_deleted: false,
        ID: id,
      },
      orderBy: {
        level: 'asc',
      },
      select: {
        ID: true,
        name: true,
        level: true,
        status: true,
        created_at: true,
        created_by: true,
        updated_by: true,
        last_updated: true,
      },
    });

    if (!data) {
      return {
        status: true,
        message: 'learning level has been deleted',
        data: null,
      };
    }

    const result: IGetLearningLevelDetailResponseDto = {
      id: data.ID,
      name: data.name,
      level: data.level,
      status: data.status,
      created_at: data.created_at ? data.created_at.toISOString() : null,
      created_by: data.created_by,
      last_updated: data.last_updated ? data.last_updated.toISOString() : null,
      updated_by: data.updated_by,
    };

    return {
      status: true,
      message: 'success get learning level',
      data: result,
    };
  } catch (error) {
    throw handleError(error);
  }
};
