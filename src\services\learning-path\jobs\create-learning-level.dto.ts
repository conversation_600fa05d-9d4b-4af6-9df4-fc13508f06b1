import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { CCreateLearningLevelBodyDto } from '../dto/create-learning-level.dto';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { HttpStatus } from '@nestjs/common';

export const JCreateLearningLevel = async (
  prisma: PrismaService,
  body: CCreateLearningLevelBodyDto,
): Promise<IGlobalResponseDto> => {
  try {
    const { level, name, status } = body;

    const isExist = await prisma.levels.findFirst({
      where: {
        is_deleted: false,
        OR: [
          {
            level: level,
          },
          {
            name: name,
          },
        ],
      },
    });

    if (isExist) {
      const isLevelExist = isExist.level === level;

      throw handleError({
        status: HttpStatus.BAD_REQUEST,
        message: `data ${isLevelExist ? 'level' : 'name'} already exists`,
      });
    }

    await prisma.levels.create({
      data: {
        level: level,
        name: name,
        status: status,
      },
    });

    return {
      status: true,
      message: 'success insert learning level',
      data: null,
    };
  } catch (error) {
    throw handleError(error);
  }
};
