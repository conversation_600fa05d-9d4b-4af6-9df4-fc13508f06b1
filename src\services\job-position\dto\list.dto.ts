import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

const getJobPositionListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z.enum(['job_name', 'job_function']).optional(),
    job_position_type: z.string().optional(),
    level: z.coerce.number().optional(),
    // is_neop: z.coerce.boolean().optional(),
    is_neop: z
      .preprocess((val) => {
        if (typeof val === 'string') return val === 'true' || val === '1';
        if (typeof val === 'number') return val === 1;
        return val;
      }, z.boolean())
      .optional(),
    starter_modul_priority: z.string().optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetJobPositionListQueryDto extends createZodDto(
  getJobPositionListQuerySchema,
) {}

export interface IGetJobPositionListResponseDto {
  id: number;
  job_id: string | null;
  job_name: string | null;
  entity_id: number | null;
  entity_name: string | null;
  department_name: string | null;
  job_function: string | null;
  job_position_type: string | null;
  level: number | null;
  is_need_neop: boolean | null;
  is_need_welcoming_kit: boolean | null;
  starter_module_priority: string | null;
  last_updated: string | null;
  updated_by: string | null;
  is_active: boolean | null;
}
