import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import z from 'zod';

const getCertificateListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z
      .enum(['npk', 'user_id', 'name', 'module_name', 'level', 'attempt'])
      .optional(),
    module_type: z
      .union([z.string(), z.array(z.string())])
      .optional()
      .transform((val) => {
        if (typeof val === 'string') return [val];
        return val;
      }),
    status: z.string().optional(),
    issue_date_start: z.string().optional(),
    issue_date_end: z.string().optional(),
    expired_date_start: z.string().optional(),
    expired_date_end: z.string().optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetCertificateListQueryDto extends createZodDto(
  getCertificateListQuerySchema,
) {}

export interface IGetCertificateListResponseDto {
  id: number | null;
  user_id: number | null;
  npk: string | null;
  name: string | null;
  module_id: number | null;
  module_name: string | null;
  module_type: string | null;
  level_id: number | null;
  level: number | null;
  module_assignment_id: number | null;
  attempt: string | null;
  issued_date: string | null;
  expired_date: string | null;
  status: string | null;
}
