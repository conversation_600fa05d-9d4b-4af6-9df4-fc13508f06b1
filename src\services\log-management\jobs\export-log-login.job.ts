import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { Workbook } from 'exceljs';
import { Prisma, user_log_login } from '@prisma/client';
import { CGetExportLogLoginListQueryDto } from '../dto/export.log-login.dto';

export const JExportLogLoginList = async (
  prisma: PrismaService,
  query: CGetExportLogLoginListQueryDto,
) => {
  try {
    const { search, search_by } = query;

    const filter: Prisma.user_log_loginWhereInput = {};

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.user_log_login.findMany({
      where: filter,
      orderBy: {
        ID: 'desc',
      },
    });

    return await createFile(data);
  } catch (error: any) {
    throw handleError(error);
  }
};

const createFile = async (data: user_log_login[]) => {
  const workbook = new Workbook();

  const ws = workbook.addWorksheet();

  // 1) Definisikan columns (pakai header & key seperti semula)
  const columns = [
    { header: 'ID', key: 'ID' },
    { header: 'Npk', key: 'npk' },
    { header: 'Start Date', key: 'start_date' },
    { header: 'End Date', key: 'end_date' },
    { header: 'Created Date', key: 'created_date' },
    { header: 'Updated Date', key: 'updated_date' },
  ];

  ws.columns = columns;

  // 2) Kosongkan row-1 (exceljs menulis header di sini secara default)
  ws.getRow(1).values = new Array(columns.length + 1).fill(null);

  const titleCell = ws.getCell(1, 1);

  titleCell.alignment = { horizontal: 'center' };
  titleCell.font = { bold: true };

  // 3) Row 2: Header manual (bold)
  const headerRow = ws.getRow(2);
  columns.forEach((c, idx) => {
    const cell = headerRow.getCell(idx + 1);
    cell.value = c.header;
    cell.font = { bold: true };
    cell.alignment = { horizontal: 'center' };
  });
  headerRow.commit();

  // 4) Data mulai baris 3
  data.forEach((val) => ws.addRow(val));

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};
