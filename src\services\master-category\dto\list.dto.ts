import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const getCategoryListQuerySchema = z
  .object({
    page: z.coerce.number().optional().default(1),
    limit: z.coerce.number().optional(),
    search: z.union([z.string(), z.number(), z.date()]).optional(),
    search_by: z
      .enum(['category_id', 'category_name', 'created_by', 'created_at'])
      .optional(),
  })
  .refine(
    (data) => {
      if (data.search_by === 'category_id') {
        return (
          data.search === undefined ||
          typeof data.search === 'number' ||
          !isNaN(Number(data.search))
        );
      }
      if (data.search_by === 'created_at') {
        return (
          data.search === undefined ||
          data.search instanceof Date ||
          !isNaN(Date.parse(String(data.search)))
        );
      }
      return true;
    },
    {
      message:
        'search must be number if search_by=category_id, or date if search_by=created_at',
      path: ['search'],
    },
  )
  .transform((data) => {
    if (data.search_by === 'category_id' && typeof data.search === 'string') {
      return { ...data, search: Number(data.search) };
    }
    if (data.search_by === 'created_at' && typeof data.search === 'string') {
      return { ...data, search: new Date(data.search) };
    }
    return data;
  });

export class CGetCategoryListQueryDto extends createZodDto(
  getCategoryListQuerySchema,
) {}

export interface IGetCategoryListResponseDto {
  id: number;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
