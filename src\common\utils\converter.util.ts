import path from 'path';
import sharp from 'sharp';

export const convertFilesToWebp = async (
  files: Express.Multer.File[] = [],
  quality = 80,
): Promise<Express.Multer.File[]> => {
  if (!files.length) return files;

  const results: Express.Multer.File[] = [];
  for (const file of files) {
    const ext = path.extname(file.originalname).toLowerCase();
    if (!['.png', '.jpg', '.jpeg', '.webp'].includes(ext)) continue;

    // pastikan file.buffer ada (karena memoryStorage)
    const webpBuffer = await sharp(file.buffer)
      .rotate()
      .webp({ quality })
      .toBuffer();

    results.push({
      ...file,
      buffer: webpBuffer,
      mimetype: 'image/webp',
      originalname: file.originalname.replace(/\.[^.]+$/, '.webp'),
    });
  }
  return results;
};
