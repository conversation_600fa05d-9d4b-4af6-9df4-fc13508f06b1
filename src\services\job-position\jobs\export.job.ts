import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { Prisma } from '@prisma/client';
import { CExportJobPositionQueryDto } from '../dto/export.dto';
import { Workbook } from 'exceljs';

interface IJobData {
  job_id: string;
  job_name: string | null;
  job_position_type: string | null;
  department_name: string | null;
}

export const JExportJobPositionList = async (
  prisma: PrismaService,
  query: CExportJobPositionQueryDto,
) => {
  try {
    const { search, search_by } = query;

    const filter: Prisma.job_namesWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
      level: query.level,
      job_position_type: query.job_position_type,
      is_need_neop: query.is_neop,
      starter_module_priority: query.starter_modul_priority,
      is_active: true,
    };

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
          },
        },
      ];
    }

    const data = await prisma.job_names.findMany({
      where: filter,
      select: {
        job_id: true,
        job_name: true,
        department_name: true,
        job_position_type: true,
      },
      orderBy: {
        last_updated: 'asc',
      },
    });

    return await createFile(data);
  } catch (error: any) {
    throw handleError(error);
  }
};

const createFile = async (data: IJobData[]) => {
  const workbook = new Workbook();

  const ws = workbook.addWorksheet();

  // 1) Definisikan columns (pakai header & key seperti semula)
  const columns = [
    { header: 'Job Position ID', key: 'job_id' },
    { header: 'Job Position Name', key: 'job_name' },
    { header: 'Job Positon Type', key: 'job_position_type' },
    { header: 'Department', key: 'department_name' },
  ];

  ws.columns = columns;

  // 2) Kosongkan row-1 (exceljs menulis header di sini secara default)
  ws.getRow(1).values = new Array(columns.length + 1).fill(null);

  const titleCell = ws.getCell(1, 1);

  titleCell.alignment = { horizontal: 'center' };
  titleCell.font = { bold: true };

  // 3) Row 2: Header manual (bold)
  const headerRow = ws.getRow(2);
  columns.forEach((c, idx) => {
    const cell = headerRow.getCell(idx + 1);
    cell.value = c.header;
    cell.font = { bold: true };
    cell.alignment = { horizontal: 'center' };
  });
  headerRow.commit();

  // 4) Data mulai baris 3
  data.forEach((val) => ws.addRow(val));

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};
