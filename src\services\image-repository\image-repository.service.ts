import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CCreateImageRepositoryBodyDto } from './dto/create.dto';
import { JCreateImageRepository } from './jobs/create.job';
import { CGetImageRepositoryListQueryDto } from './dto/list.dto';
import { JGetImageRepositoryList } from './jobs/list.job';
import { CGetImageRepositoryDetailParamsDto } from './dto/detail.dto';
import { JGetImageRepositoryDetail } from './jobs/detail.job';
import {
  CUpdateImageRepositoryBodyDto,
  CUpdateImageRepositoryParamsDto,
} from './dto/update.dto';
import { JUpdateImageRepository } from './jobs/update.job';

@Injectable()
export class ImageRepositoryService {
  constructor(private readonly prisma: PrismaService) {}

  getImageRepositoryList = (query: CGetImageRepositoryListQueryDto) =>
    JGetImageRepositoryList(this.prisma, query);
  getImageRepositoryDetail = (params: CGetImageRepositoryDetailParamsDto) =>
    JGetImageRepositoryDetail(this.prisma, params);
  createImageRepository = (
    body: CCreateImageRepositoryBodyDto,
    file?: Express.Multer.File,
  ) => JCreateImageRepository(this.prisma, body, file);
  updateImageRepository = (
    params: CUpdateImageRepositoryParamsDto,
    body: CUpdateImageRepositoryBodyDto,
    file?: Express.Multer.File,
  ) => JUpdateImageRepository(this.prisma, params, body, file);
}
