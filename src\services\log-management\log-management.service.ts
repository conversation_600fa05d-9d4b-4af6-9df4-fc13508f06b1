import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CGetLogLoginListQueryDto } from './dto/list-log-login.dto';
import { JGetLogLoginList } from './jobs/list-log-login.job';
import { JExportLogLoginList } from './jobs/export-log-login.job';
import { CGetExportLogLoginListQueryDto } from './dto/export.log-login.dto';

@Injectable()
export class LogManagementService {
  constructor(private readonly prisma: PrismaService) {}

  getLogLoginList = (query: CGetLogLoginListQueryDto) =>
    JGetLogLoginList(this.prisma, query);
  getExportLogLogin = (query: CGetExportLogLoginListQueryDto) =>
    JExportLogLoginList(this.prisma, query);
}
