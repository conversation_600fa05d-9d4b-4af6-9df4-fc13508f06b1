import { handleError } from 'src/common/utils/error.util';
import { CCreateUserBodyDto } from '../dto/create.dto';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { BadRequestException } from '@nestjs/common';
import { uploadSignatureImage } from './update.job';

export const JCreateUser = async (
  prisma: PrismaService,
  body: CCreateUserBodyDto,
  file: Express.Multer.File,
): Promise<IGlobalResponseDto> => {
  try {
    const upload = await uploadSignatureImage(file);

    const find = await prisma.user_details.findFirst({
      where: {
        AND: [
          {
            OR: [
              {
                is_deleted: null,
              },
              {
                is_deleted: false,
              },
            ],
          },
          {
            OR: [
              {
                npk: body.npk,
              },
              {
                email: body.email,
              },
            ],
          },
        ],
      },
    });

    if (find) {
      const key = body.npk === find.npk ? 'npk' : 'email';
      throw new BadRequestException(`failed insert data, ${key} already exist`);
    }

    await prisma.user_details.create({
      data: {
        npk: body.npk,
        email: body.email,
        point: body.point,
        forum_title: body.forum_title,
        name: body.name,
        job_name: body.job_name,
        password: body.password,
        job_name_id: body.job_name_id,
        phone_number: body.phone_number,
        user_type_id: body.user_type_id,
        user_role_id: body.user_role_id,
        supervisor_id: body.supervisor_id,
        supervisor_name: body.supervisor_name,
        supervisor_npk: body.supervisor_npk,
        is_active: body.is_active,
        is_new_user: body.is_new_user,
        is_deleted: body.is_deleted,
        created_by: body.created_by,
        updated_by: body.updated_by,
        forum_title_id: body.forum_title_id,
        entity_id: body.entity_id,
        is_need_neop: body.is_need_neop,
        is_need_welcoming_kit: body.is_need_welcoming_kit,
        second_email: body.second_email,
        signature: upload,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });

    return {
      status: true,
      message: 'success insert data',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
