import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { CGetUserTypeQueryDto, IGetUserTypeResponseDto } from '../dto/type.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetUserType = async (
  prisma: PrismaService,
  query: CGetUserTypeQueryDto,
): Promise<IGlobalResponseDto<IGetUserTypeResponseDto[]>> => {
  try {
    const { search } = query;

    const filter: Prisma.user_typeWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search) {
      filter.AND = [
        {
          user_type_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.user_type.findMany({
      where: filter,
      select: {
        id: true,
        user_type_name: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    if (!data.length) {
      return {
        status: true,
        message: 'no data found',
        data: [],
      };
    }

    const result: IGetUserTypeResponseDto[] = data.map((item) => ({
      type_id: item.id,
      type_name: item.user_type_name,
    }));

    return {
      status: true,
      message: 'success get data type user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
