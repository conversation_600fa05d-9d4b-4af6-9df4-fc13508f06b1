import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetLearningCodeListQueryDto,
  IGetLearningCodeListResponseDto,
} from '../dto/get-learning-code.dto';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { Prisma } from '@prisma/client';
import dayjs from 'dayjs';

export const JGetLearningCodeList = async (
  prisma: PrismaService,
  query: CGetLearningCodeListQueryDto,
): Promise<IGlobalResponseDto<IGetLearningCodeListResponseDto[] | null>> => {
  try {
    const { limit, page, search, search_by } = query;

    const { skip, take } = getOffset(page, limit);

    const filter: Prisma.learning_pathWhereInput = {
      is_deleted: false,
      status: 'active',
    };

    filter.AND = [];

    if (search && search_by) {
      if (['code', 'name', 'created_by', 'updated_by'].includes(search_by)) {
        filter.AND.push({
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        });
      }

      if (['job_name'].includes(search_by)) {
        filter.AND.push({
          learning_job: {
            some: {
              job_names: {
                job_name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
          },
        });
      }

      if (['modul_name'].includes(search_by)) {
        filter.AND.push({
          learning_modules: {
            some: {
              moduls: {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
          },
        });
      }
    }

    const [data, total] = await Promise.all([
      prisma.learning_path.findMany({
        where: filter,
        select: {
          ID: true,
          code: true,
          name: true,
          learning_job: {
            select: {
              job_name_id: true,
              job_names: {
                select: {
                  job_id: true,
                  job_name: true,
                },
              },
            },
          },
          learning_modules: {
            select: {
              modul_id: true,
              moduls: {
                select: {
                  name: true,
                },
              },
            },
          },
          created_by: true,
          updated_by: true,
          last_updated: true,
          status: true,
          created_at: true,
        },
        skip,
        take,
        orderBy: {
          last_updated: 'desc',
        },
      }),
      prisma.learning_path.count({
        where: filter,
      }),
    ]);

    if (total === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: null,
      };
    }

    if (!data.length) {
      return {
        status: true,
        message: 'data not found',
        data: null,
      };
    }

    const response: IGetLearningCodeListResponseDto[] = data.map((item) => ({
      ID: item.ID,
      code: item.code,
      name: item.name,
      related_job: item.learning_job.map((job) => ({
        job_name_id: job.job_name_id,
        job_name: (job.job_names?.job_name as string) ?? null,
      })),
      related_modul: item.learning_modules.map((modul) => ({
        modul_id: modul.modul_id,
        modul_name: (modul.moduls?.name as string) ?? null,
      })),
      created_at: dayjs(item.created_at).toISOString(),
      created_by: item.created_by,
      last_updated: dayjs(item.last_updated).toISOString(),
      updated_by: item.updated_by,
      status: item.status,
    }));

    return {
      status: true,
      message: 'success get data learning code',
      data: response,
      pagination: createPagination(
        page,
        limit,
        total,
        '/admin/learning/code/list',
        query,
      ),
    };
  } catch (error) {
    throw handleError(error);
  }
};
