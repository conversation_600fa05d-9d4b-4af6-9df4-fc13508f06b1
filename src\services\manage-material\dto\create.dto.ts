import { createZodDto } from 'nestjs-zod';
import z from 'zod';

export const createMaterialSchema = z.object({
  file_type: z.enum(['audio', 'video', 'document']),
  data: z.preprocess(
    (val) => {
      if (typeof val === 'string') {
        try {
          return JSON.parse(val);
        } catch {
          return val;
        }
      }
      return val;
    },
    z.object({
      feature: z.enum(['OnlineLearning', 'InClassTraining']),
      category_id: z
        .any() // string/array
        .optional()
        .transform((val) => {
          if (!val) return [];

          // "[1,2,3]"
          if (typeof val === 'string') {
            try {
              const parsed = JSON.parse(val);
              if (Array.isArray(parsed)) return parsed.map(Number);
            } catch {
              // "1,2,3"
              return val.split(',').map((v) => Number(v.trim()));
            }
          }

          // ["1","2","3"]
          if (Array.isArray(val)) {
            return val.map((v) => Number(v));
          }

          return [];
        })
        .refine((arr) => arr.every((n) => typeof n === 'number' && !isNaN(n)), {
          message: 'category id must be numbers',
        }),
      level_id: z
        .any() // string/array
        .optional()
        .transform((val) => {
          if (!val) return [];

          // "[1,2,3]"
          if (typeof val === 'string') {
            try {
              const parsed = JSON.parse(val);
              if (Array.isArray(parsed)) return parsed.map(Number);
            } catch {
              // "1,2,3"
              return val.split(',').map((v) => Number(v.trim()));
            }
          }

          // ["1","2","3"]
          if (Array.isArray(val)) {
            return val.map((v) => Number(v));
          }

          return [];
        })
        .refine((arr) => arr.every((n) => typeof n === 'number' && !isNaN(n)), {
          message: 'level id must be numbers',
        }),
    }),
  ),
});

export class CCreateMaterialBodyDto extends createZodDto(
  createMaterialSchema,
) {}

export interface IMaterialImage {
  file: Express.Multer.File[];
}

export type TFileType = CCreateMaterialBodyDto['file_type'];
