# API Creation Rules for Lemon CMS NestJS Project

## Overview
This document provides step-by-step instructions for AI assistants to create APIs following the established patterns in the Lemon CMS project.

## Project Architecture Pattern

**Flow**: Controller → Service → Job → Prisma → Database

**Structure**:
```
src/services/{feature-name}/
├── dto/
│   ├── {action}.dto.ts
│   └── ...
├── jobs/
│   ├── {action}.job.ts
│   └── ...
├── {feature-name}.controller.ts
├── {feature-name}.service.ts
└── {feature-name}.module.ts
```

## Step-by-Step API Creation Process

### Step 1: Analyze User Requirements
When user provides:
- **Expected endpoint**: Extract HTTP method, route path, and parameters
- **Request body**: Identify required/optional fields and validation rules
- **Logic**: Understand business requirements and data processing needs
- **Related table**: Identify Prisma models and database relationships
- **Response body**: Define success and error response structures

### Step 2: Create/Update DTOs

#### 2.1 Request DTO (if needed)
Create in `src/services/{feature}/dto/{action}.dto.ts`:

```typescript
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';

// Define Zod schema
const {action}Schema = z.object({
  // Define fields based on user requirements
  field_name: z.string().optional(), // or .min(1) for required
  numeric_field: z.coerce.number().optional(),
  enum_field: z.enum(['value1', 'value2']).optional(),
  boolean_field: z.string().optional().transform((val) => val === 'true'),
})
// Add pagination if it's a list endpoint
.and(globalPaginationQuerySchema); // Only for GET list endpoints

// Export DTO class
export class C{Action}Dto extends createZodDto({action}Schema) {}
```

#### 2.2 Response Interface
Add to the same DTO file:

```typescript
export interface I{Action}ResponseDto {
  // Define response structure based on user requirements
  id: number | null;
  field_name: string | null;
  // ... other fields
}
```

### Step 3: Create Job Function

Create in `src/services/{feature}/jobs/{action}.job.ts`:

```typescript
import { PrismaService } from 'src/services/prisma/prisma.service';
import { C{Action}Dto, I{Action}ResponseDto } from '../dto/{action}.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { handleError } from 'src/common/utils/error.util';

export const J{Action} = async (
  prisma: PrismaService,
  data: C{Action}Dto, // or params for path parameters
): Promise<IGlobalResponseDto<I{Action}ResponseDto[]>> => { // or single object
  try {
    // Extract data from DTO
    const { field1, field2, page, limit } = data;

    // Build Prisma filter (for GET requests)
    const filter: Prisma.{table_name}WhereInput = {
      is_deleted: false, // Standard soft delete check
      // Add other filters based on user logic
    };

    // Handle search logic if applicable
    if (search && search_by) {
      filter.AND = [{
        [search_by]: {
          contains: search,
        },
      }];
    }

    // For list endpoints with pagination
    const { skip, take } = getOffset(page, limit);
    
    const [data, count] = await Promise.all([
      prisma.{table_name}.findMany({
        where: filter,
        select: {
          // Select fields based on response interface
          ID: true,
          field_name: true,
          // ... other fields
        },
        skip,
        take,
        orderBy: { created_at: 'desc' }, // or other ordering
      }),
      prisma.{table_name}.count({ where: filter }),
    ]);

    // Transform data if needed
    const transformedData = data.map((item) => ({
      id: item.ID,
      field_name: item.field_name,
      // ... map other fields
    }));

    // Create pagination
    const pagination = createPagination({
      page,
      limit,
      total: count,
      path: '/{endpoint-path}',
    });

    return {
      status: true,
      message: 'Success message',
      data: transformedData,
      pagination, // Only for list endpoints
    };
  } catch (error) {
    throw handleError(error);
  }
};
```

### Step 4: Update Controller

Add method to `src/services/{feature}/{feature}.controller.ts`:

```typescript
import { Controller, Get, Post, Put, Delete, Param, Query, Body } from '@nestjs/common';
import { {Feature}Service } from './{feature}.service';
import { C{Action}Dto } from './dto/{action}.dto';

@Controller('/admin') // or appropriate base path
export class {Feature}Controller {
  constructor(private readonly {feature}Service: {Feature}Service) {}

  @Get('{endpoint-path}') // e.g., 'users' or 'users/:id'
  {methodName}(@Query() query: C{Action}Dto) { // or @Param(), @Body()
    return this.{feature}Service.{methodName}(query);
  }

  // For POST/PUT requests:
  @Post('{endpoint-path}')
  {methodName}(@Body() body: C{Action}Dto) {
    return this.{feature}Service.{methodName}(body);
  }

  // For DELETE requests:
  @Delete('{endpoint-path}/:id')
  {methodName}(@Param() params: C{Action}ParamsDto) {
    return this.{feature}Service.{methodName}(params);
  }
}
```

### Step 5: Update Service

Add method to `src/services/{feature}/{feature}.service.ts`:

```typescript
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { J{Action} } from './jobs/{action}.job';
import { C{Action}Dto } from './dto/{action}.dto';

@Injectable()
export class {Feature}Service {
  constructor(private readonly prisma: PrismaService) {}

  {methodName} = (data: C{Action}Dto) => J{Action}(this.prisma, data);
}
```

### Step 6: Update Module (if new feature)

If creating a new feature module, create `src/services/{feature}/{feature}.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { {Feature}Service } from './{feature}.service';
import { {Feature}Controller } from './{feature}.controller';

@Module({
  controllers: [{Feature}Controller],
  providers: [{Feature}Service],
})
export class {Feature}Module {}
```

Then add to `src/app.module.ts` imports array:
```typescript
imports: [
  // ... existing imports
  {Feature}Module,
],
```

## Response Format Standards

### Success Response
```typescript
{
  status: true,
  message: "Success message",
  data: T, // Array for lists, Object for single items
  pagination?: IGlobalPaginationDto // Only for paginated lists
}
```

### Error Response
```typescript
{
  status: false,
  message: "Error message",
  data: null
}
```

## Naming Conventions

- **DTOs**: `C{Action}{Type}Dto` (e.g., `CGetUserListQueryDto`, `CCreateUserBodyDto`)
- **Interfaces**: `I{Action}ResponseDto` (e.g., `IGetUserListResponseDto`)
- **Jobs**: `J{Action}` (e.g., `JGetUserList`, `JCreateUser`)
- **Controller methods**: Use descriptive names (e.g., `getUserList`, `createUser`)
- **Service methods**: Match controller method names
- **Files**: Use kebab-case (e.g., `user-list.dto.ts`, `create-user.job.ts`)

## Common Patterns

### Pagination (for list endpoints)
- Always include `globalPaginationQuerySchema` in query DTOs
- Use `getOffset()` and `createPagination()` utilities
- Return both data and pagination in response

### Search & Filtering
- Include `search` and `search_by` fields in DTOs
- Use `contains` for text search
- Support multiple filter fields

### Soft Delete
- Always filter by `is_deleted: false`
- For delete operations, set `is_deleted: true` instead of actual deletion

### Error Handling
- Wrap all job functions in try-catch
- Use `handleError()` utility for consistent error responses
- Return appropriate HTTP status codes

## Validation Rules

- Use Zod schemas for all input validation
- Use `z.coerce.number()` for numeric fields from query parameters
- Use `z.enum()` for predefined values
- Use `.optional()` for optional fields
- Use `.min(1)` or similar for required string fields
- Transform boolean strings with `.transform((val) => val === 'true')`

## Database Patterns

- Use Prisma `select` to only fetch needed fields
- Use `Promise.all()` for parallel database operations
- Include proper relationships with `include` when needed
- Use appropriate ordering (usually `created_at: 'desc'`)
- Handle database constraints and foreign key relationships

## Security Considerations

- Validate all inputs through DTOs
- Use parameterized queries (Prisma handles this)
- Don't expose sensitive fields in responses
- Implement proper authentication/authorization (marked with // Auth comments)

## Testing Considerations

- Ensure all endpoints return consistent response format
- Test both positive and negative cases
- Validate pagination works correctly
- Test search and filtering functionality
- Verify error handling returns appropriate messages

Follow these patterns consistently to maintain code quality and project standards.