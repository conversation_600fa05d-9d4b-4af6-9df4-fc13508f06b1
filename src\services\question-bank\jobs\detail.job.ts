import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetQuestionBankDetailParamsDto,
  IQuestionBankDetailResponseDto,
} from '../dto/detail.dto';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetQuestionBankDetail = async (
  prisma: PrismaService,
  params: CGetQuestionBankDetailParamsDto,
): Promise<IGlobalResponseDto<IQuestionBankDetailResponseDto>> => {
  try {
    const { id } = params;

    const data = await prisma.question
      .findFirstOrThrow({
        where: {
          id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        include: {
          image_repository: true,
          question_level_mapping: {
            include: {
              levels: true,
            },
          },
          question_category_mapping: {
            include: {
              category: true,
            },
          },
          test_question_mapping: {
            include: {
              sub_section: true,
            },
          },
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
      })
      .catch(() => {
        throw handleError({
          message: 'question bank not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IQuestionBankDetailResponseDto = {
      id: data.id,
      question: data.question,
      type: data.type,
      image_id: data.image_repository?.id ?? null,
      image_link: data.image_repository?.link ?? null,
      option_a: data.a,
      option_b: data.b,
      option_c: data.c,
      option_d: data.d,
      correct_answer: data.correct_answer,
      correct_answer_percentage: data?.correct_answer_percentage
        ? Number(data.correct_answer_percentage)
        : null,
      levels: data.question_level_mapping
        .filter((ml) => ml.levels)
        .map((ml) => ({
          id: ml.levels?.ID ?? null,
          level: ml.levels?.name ?? null,
        })),
      categories: data.question_category_mapping
        .filter((mc) => mc.category)
        .map((mc) => ({
          id: mc.category?.id ?? null,
          name: mc.category?.category_name ?? null,
        })),
      associated: data.test_question_mapping.map((ss) => ({
        section_id: ss.sub_section?.ID ?? null,
        section_name: ss.sub_section?.name ?? null,
      })),
      feature: data.feature,
      created_at: data.created_at ? data.created_at.toISOString() : null,
      created_by: data.created_by,
      updated_by: data.updated_by,
      last_updated: data.last_updated ? data.last_updated.toISOString() : null,
    };

    return {
      status: true,
      message: 'success get question bank',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
