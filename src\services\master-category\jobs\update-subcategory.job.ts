import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CUpdateSubCategoryDto,
  CUpdateSubCategoryParamsDto,
} from '../dto/update-subcategory.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateSubCategory = async (
  prisma: PrismaService,
  params: CUpdateSubCategoryParamsDto,
  body: CUpdateSubCategoryDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.sub_category
      .findUniqueOrThrow({
        where: {
          id: params.id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
      })
      .catch(() => {
        throw handleError({
          status: HttpStatus.NOT_FOUND,
          message: 'Subcategory not found',
        });
      });

    await prisma.$transaction(async (tx) => {
      await tx.sub_category.update({
        where: { id: params.id },
        data: {
          sub_category_name: body.subcategory_name,
          category_id: body.category_id,
          updated_by: null,
          last_updated: new Date(),
          is_deleted: body.is_deleted,
        },
      });

      const categories = await tx.sub_category.findMany({
        where: {
          OR: [{ is_deleted: false }, { is_deleted: null }],
          sub_category_name: body.subcategory_name,
        },
      });

      if (categories.length > 1) {
        throw handleError({
          status: HttpStatus.CONFLICT,
          message: 'failed, subcategory name must be unique',
        });
      }
    });

    return {
      status: true,
      message: 'success update data subcategory',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
