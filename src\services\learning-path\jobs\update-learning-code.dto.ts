import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CUpdateLearningCodeBodyDto,
  CUpdateLearningCodeParamsDto,
} from '../dto/update-learning-code.dto';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateLearningCode = async (
  prisma: PrismaService,
  params: CUpdateLearningCodeParamsDto,
  body: CUpdateLearningCodeBodyDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const [find, isAlreadyExists] = await Promise.all([
      prisma.learning_path.findUnique({
        where: {
          ID: params.id,
          status: 'active',
          is_deleted: false,
        },
      }),
      prisma.learning_path.findFirst({
        where: {
          ID: { not: params.id }, // Tambahkan ini untuk mengecualikan data yang sedang di-update
          OR: [{ is_deleted: false }, { is_deleted: null }],
          AND: [
            {
              OR: [
                {
                  code: body.learning_code,
                },
                {
                  name: body.learning_code_name,
                },
              ],
            },
          ],
        },
        select: {
          code: true,
          name: true,
        },
      }),
    ]);

    if (!find) {
      throw handleError({
        status: HttpStatus.NOT_FOUND,
        message: 'data not found',
      });
    }

    if (isAlreadyExists) {
      throw handleError({
        status: HttpStatus.BAD_REQUEST,
        message:
          isAlreadyExists.code === body.learning_code
            ? 'learning code is exist'
            : 'learning code name is exist',
      });
    }

    await prisma.$transaction(async (tx) => {
      // 1. Hapus learning_job yang sudah ada
      await tx.learning_job.deleteMany({
        where: {
          learning_path_id: find.ID,
        },
      });

      // 2. Buat learning_job yang baru
      await tx.learning_job.createMany({
        data: body.job_name_id.map((id) => ({
          job_name_id: id,
          learning_path_id: find.ID,
          status: 'active',
        })),
      });

      // 3. Update learning_path itu sendiri
      await tx.learning_path.update({
        where: {
          ID: find.ID,
        },
        data: {
          code: body.learning_code,
          name: body.learning_code_name,
        },
      });
    });

    return {
      status: true,
      message: 'success update learning code',
      data: null,
    };
  } catch (error) {
    throw handleError(error);
  }
};
