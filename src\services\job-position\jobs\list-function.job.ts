import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetJobFunctionListQueryDto,
  IGetJobFunctionListResponseDto,
} from '../dto/list-function.dto';

export const JGetJobFunctionList = async (
  prisma: PrismaService,
  query: CGetJobFunctionListQueryDto,
): Promise<IGlobalResponseDto<IGetJobFunctionListResponseDto[]>> => {
  try {
    const data = await prisma.job_functions.findMany({
      where: {
        name: { contains: query.search, mode: 'insensitive' },
      },
      select: {
        ID: true,
        name: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetJobFunctionListResponseDto[] = data.map((item) => ({
      id: item.ID,
      function_name: item.name,
    }));

    return {
      status: true,
      message: 'success get data job function',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
