import { handleError } from 'src/common/utils/error.util';
import { CUpdateUserBodyDto } from '../dto/update.dto';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { BadRequestException } from '@nestjs/common';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import {
  convertImageToWebp,
  removeExtension,
} from 'src/common/utils/image-processor.util';

export const JupdateUser = async (
  prisma: PrismaService,
  id: number,
  body: CUpdateUserBodyDto,
  file?: Express.Multer.File,
): Promise<IGlobalResponseDto> => {
  try {
    const upload = file ? await uploadSignatureImage(file) : undefined;

    const [findUser, findExist] = await Promise.all([
      prisma.user_details.findFirst({
        where: {
          AND: [
            {
              OR: [
                {
                  is_deleted: null,
                },
                {
                  is_deleted: false,
                },
              ],
            },
            {
              ID: id,
            },
          ],
        },
      }),
      prisma.user_details.findFirst({
        where: {
          AND: [
            {
              OR: [
                {
                  is_deleted: null,
                },
                {
                  is_deleted: false,
                },
              ],
            },
            {
              OR: [
                {
                  npk: body.npk,
                },
                {
                  email: body.email,
                },
              ],
            },
          ],
        },
      }),
    ]);

    if (!findUser) throw new BadRequestException('data not found');

    if (findExist && findExist.ID !== id && (body.npk || body.email)) {
      const key = body.npk === findExist.npk ? 'npk' : 'email';
      throw new BadRequestException(`failed insert data, ${key} already exist`);
    }

    await prisma.user_details.update({
      where: {
        ID: id,
      },
      data: {
        npk: body.npk,
        email: body.email,
        point: body.point,
        forum_title: body.forum_title,
        name: body.name,
        job_name: body.job_name,
        password: body.password,
        job_name_id: body.job_name_id,
        phone_number: body.phone_number,
        user_type_id: body.user_type_id,
        user_role_id: body.user_role_id,
        supervisor_id: body.supervisor_id,
        supervisor_name: body.supervisor_name,
        supervisor_npk: body.supervisor_npk,
        is_active: body.is_active,
        is_new_user: body.is_new_user,
        is_deleted: body.is_deleted,
        created_by: body.created_by,
        updated_by: body.updated_by,
        forum_title_id: body.forum_title_id,
        entity_id: body.entity_id,
        is_need_neop: body.is_need_neop,
        signature: upload,
        is_need_welcoming_kit: body.is_need_welcoming_kit,
        updated_at: new Date(),
      },
    });

    return {
      status: true,
      message: 'success update data',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

export const uploadSignatureImage = async (file: Express.Multer.File) => {
  let buffer = file.buffer;
  let filePathName = file.originalname;
  const mimetype = 'image/webp';

  buffer = await convertImageToWebp(buffer);
  filePathName = `users/signature/${removeExtension(filePathName)}.webp`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': mimetype,
  });

  return filePathName;
};
