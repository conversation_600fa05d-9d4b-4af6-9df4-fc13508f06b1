import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetUserListQueryDto,
  IGetUserListResponseDto,
} from '../dto/user-list.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetUserList = async (
  prisma: PrismaService,
  query: CGetUserListQueryDto,
): Promise<IGlobalResponseDto<IGetUserListResponseDto[]>> => {
  try {
    const { search } = query;

    const filter: Prisma.user_detailsWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search) {
      filter.AND = [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.user_details.findMany({
      where: filter,
      select: {
        ID: true,
        name: true,
        npk: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    if (!data.length) {
      return {
        status: true,
        message: 'no data found',
        data: [],
      };
    }

    const result: IGetUserListResponseDto[] = data.map((item) => ({
      user_id: item.ID,
      user_name: item.name,
      user_npk: item.npk,
    }));

    return {
      status: true,
      message: 'success get data user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
