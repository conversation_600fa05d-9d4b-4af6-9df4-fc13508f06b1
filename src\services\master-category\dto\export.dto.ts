import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const exportCategoryQuerySchema = z
  .object({
    search: z.union([z.string(), z.number(), z.date()]).optional(),
    search_by: z
      .enum(['category_id', 'category_name', 'created_by', 'created_at'])
      .optional(),
  })
  .refine(
    (data) => {
      if (data.search_by === 'category_id') {
        return (
          data.search === undefined ||
          typeof data.search === 'number' ||
          !isNaN(Number(data.search))
        );
      }
      if (data.search_by === 'created_at') {
        return (
          data.search === undefined ||
          data.search instanceof Date ||
          !isNaN(Date.parse(String(data.search)))
        );
      }
      return true;
    },
    {
      message:
        'search must be number if search_by=category_id, or date if search_by=created_at',
      path: ['search'],
    },
  )
  .transform((data) => {
    if (data.search_by === 'category_id' && typeof data.search === 'string') {
      return { ...data, search: Number(data.search) };
    }
    if (data.search_by === 'created_at' && typeof data.search === 'string') {
      return { ...data, search: new Date(data.search) };
    }
    return data;
  });

export class CExportCategoryQueryDto extends createZodDto(
  exportCategoryQuerySchema,
) {}
