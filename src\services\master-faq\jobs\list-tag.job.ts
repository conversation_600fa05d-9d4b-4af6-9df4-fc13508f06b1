import { PrismaService } from 'src/services/prisma/prisma.service';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { handleError } from 'src/common/utils/error.util';
import {
  CGetTagListQueryDto,
  IGetTagListResponseDto,
} from '../dto/list-tag.dto';

export const JGetTagList = async (
  prisma: PrismaService,
  query: CGetTagListQueryDto,
): Promise<IGlobalResponseDto<IGetTagListResponseDto[]>> => {
  try {
    const data = await prisma.tags.findMany({
      where: {
        name: { contains: query.search, mode: 'insensitive' },
      },
      select: {
        ID: true,
        name: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    const result: IGetTagListResponseDto[] = data.map((item) => ({
      tag_id: item.ID,
      tag_name: item.name,
    }));

    return {
      status: true,
      message: 'success get data tag',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
