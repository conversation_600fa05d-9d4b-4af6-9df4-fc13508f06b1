import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { JGetUserList } from './jobs/list.job';
import { JGetUserList as JGetUserList2 } from './jobs/user-list.job';
import { CGetUserListQueryDto } from './dto/list.dto';
import { CGetUserListQueryDto as CGetUserListQueryDto2 } from './dto/user-list.dto';
import { CGetUserDetailParamsDto } from './dto/detail.dto';
import { JGetUserDetail } from './jobs/detail.job';
import { CGetUserRoleQueryDto } from './dto/role.dto';
import { JGetUserRole } from './jobs/role.job';
import { CGetUserTypeQueryDto } from './dto/type.dto';
import { JGetUserType } from './jobs/type.job';
import { CGetUserEntityQueryDto } from './dto/entity.dto';
import { JGetUserEntity } from './jobs/entity.job';
import { CGetJobPositionQueryDto } from './dto/job-position.dto';
import { JGetJobPosition } from './jobs/job-position.job';
import { CGetForumTitleQueryDto } from './dto/forum-title.dto';
import { JGetForumTitle } from './jobs/forum-title.job';
import { CGetUserDownloadQueryDto } from './dto/download.dto';
import { JGetUserDownload } from './jobs/download.job';
import { JCreateUser } from './jobs/create.job';
import { CCreateUserBodyDto } from './dto/create.dto';
import { JupdateUser } from './jobs/update.job';
import { CUpdateUserBodyDto } from './dto/update.dto';

@Injectable()
export class UserManagementService {
  constructor(private readonly prisma: PrismaService) {}

  getAll = (query: CGetUserListQueryDto) => JGetUserList(this.prisma, query);
  getDetail = (params: CGetUserDetailParamsDto) =>
    JGetUserDetail(this.prisma, params);
  getUserRole = (query: CGetUserRoleQueryDto) =>
    JGetUserRole(this.prisma, query);
  getUserType = (query: CGetUserTypeQueryDto) =>
    JGetUserType(this.prisma, query);
  getUserEntity = (query: CGetUserEntityQueryDto) =>
    JGetUserEntity(this.prisma, query);
  getJobPosition = (query: CGetJobPositionQueryDto) =>
    JGetJobPosition(this.prisma, query);
  getUserList = (query: CGetUserListQueryDto2) =>
    JGetUserList2(this.prisma, query);
  getForumTitle = (query: CGetForumTitleQueryDto) =>
    JGetForumTitle(this.prisma, query);
  getUserDownload = (query: CGetUserDownloadQueryDto) =>
    JGetUserDownload(this.prisma, query);
  createUser = (body: CCreateUserBodyDto, file: Express.Multer.File) =>
    JCreateUser(this.prisma, body, file);
  updateUser = (
    id: number,
    body: CUpdateUserBodyDto,
    file: Express.Multer.File,
  ) => JupdateUser(this.prisma, id, body, file);
}
