import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetSliderDetailParamsDto,
  IGetSliderDetailResponseDto,
} from '../dto/detail-slider.dto';

export const JGetSliderDetail = async (
  prisma: PrismaService,
  params: CGetSliderDetailParamsDto,
): Promise<IGlobalResponseDto<IGetSliderDetailResponseDto | null>> => {
  try {
    const { id } = params;

    const data = await prisma.sliders.findUnique({
      where: {
        ID: id,
        OR: [{ is_deleted: false }, { is_deleted: null }],
      },
      select: {
        ID: true,
        slider: true,
        slider_name: true,
        slider_mobile: true,
        link: true,
        is_deleted: true,
      },
    });

    if (!data) {
      return {
        status: true,
        message: 'slider id is not found',
        data: null,
      };
    }

    if (data.is_deleted) {
      return {
        status: true,
        message: 'slider is deleted',
        data: null,
      };
    }

    const result: IGetSliderDetailResponseDto = {
      id: data.ID,
      slider_name: data.slider_name,
      slider_desktop: data.slider,
      slider_mobile: data.slider_mobile,
      link: data.link,
    };

    return {
      status: true,
      message: 'success get data slider',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
