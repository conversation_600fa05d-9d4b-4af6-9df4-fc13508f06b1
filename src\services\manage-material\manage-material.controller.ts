import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { ManageMaterialService } from './manage-material.service';
import { CGetManageMaterialListQueryDto } from './dto/list.dto';
import { CGetManageMaterialDetailParamsDto } from './dto/detail.dto';
import { FormDataGuard } from 'src/common/guards/form-data.guard';
import { FileUploadInterceptor } from 'src/common/interceptors/file-upload.interceptor';
import { CCreateMaterialBodyDto } from './dto/create.dto';
import {
  CUpdateMaterialBodyDto,
  CUpdateMaterialParamsDto,
} from './dto/update.dto';
import { CGetPresignUrlQueryDto } from './dto/presign-url.dto';
import {
  CDeleteMaterialBodyDto,
  CDeleteMaterialParamsDto,
} from './dto/delete.dto';

@Controller('/admin/learning')
export class ManageMaterialController {
  constructor(
    private readonly masterManageMaterialService: ManageMaterialService,
  ) {}

  // Auth
  @Get('material/list')
  getManageMaterialList(@Query() query: CGetManageMaterialListQueryDto) {
    return this.masterManageMaterialService.getManageMaterialList(query);
  }

  //   Auth
  @Get('material/detail/:id')
  getManageMaterialDetail(@Param() params: CGetManageMaterialDetailParamsDto) {
    return this.masterManageMaterialService.getManageMaterialDetail(params);
  }

  //   Auth
  @Get('material/url')
  getPresignMaterialUrl(@Query() query: CGetPresignUrlQueryDto) {
    return this.masterManageMaterialService.getPresignedUrlMaterial(query);
  }

  //   Auth
  @Post('material/delete/:id')
  deleteMaterial(
    @Param() params: CDeleteMaterialParamsDto,
    @Body() body: CDeleteMaterialBodyDto,
  ) {
    return this.masterManageMaterialService.deleteMaterial(params, body);
  }

  // Auth
  @Post('material/insert')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    allowedMimeTypes: /^(audio\/mpeg|video\/mp4|application\/pdf)$/,
    maxSizeMB: 120,
  })
  async createMaterial(
    @Body() body: CCreateMaterialBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.masterManageMaterialService.createMaterial(body, file);
  }

  // Auth
  @Post('material/update/:id')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    allowedMimeTypes: /^(audio\/mpeg|video\/mp4|application\/pdf)$/,
    maxSizeMB: 120,
  })
  async updateMaterial(
    @Param() params: CUpdateMaterialParamsDto,
    @Body() body: CUpdateMaterialBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.masterManageMaterialService.updateMaterial(params, body, file);
  }
}
