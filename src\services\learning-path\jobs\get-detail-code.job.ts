import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetLearningCodeDetailParamsDto,
  IGetLearningCodeDetailResponseDto,
} from '../dto/get-learning-code.dto';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetLearningCodeDetail = async (
  prisma: PrismaService,
  params: CGetLearningCodeDetailParamsDto,
): Promise<IGlobalResponseDto<IGetLearningCodeDetailResponseDto | null>> => {
  try {
    const { id } = params;

    const data = await prisma.learning_path.findUnique({
      where: {
        ID: id,
        status: 'active',
        is_deleted: false,
      },
      select: {
        ID: true,
        code: true,
        name: true,
        learning_job: {
          select: {
            job_name_id: true,
          },
        },
        created_at: true,
        created_by: true,
        last_updated: true,
        updated_by: true,
        status: true,
      },
    });

    if (!data) {
      return {
        status: true,
        message: 'data not found',
        data: null,
      };
    }

    const result: IGetLearningCodeDetailResponseDto = {
      ID: data.ID,
      code: data.code,
      name: data.name,
      related_job: data.learning_job.map((item) => item.job_name_id) ?? [],
      created_at: data.created_at ? data.created_at.toISOString() : null,
      created_by: data.created_by,
      last_updated: data.last_updated ? data.last_updated.toISOString() : null,
      updated_by: data.updated_by,
      status: data.status,
    };

    return {
      status: true,
      message: 'success get learning code',
      data: result,
    };
  } catch (error) {
    throw handleError(error);
  }
};
