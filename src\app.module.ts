import {
  Zod<PERSON><PERSON><PERSON><PERSON>ip<PERSON>,
  ZodSerializerInterceptor,
  ZodSerializationException,
} from 'nestjs-zod';
import {
  APP_PIPE,
  APP_INTERCEPTOR,
  APP_FILTER,
  BaseExceptionFilter,
} from '@nestjs/core';
import { ZodError } from 'zod';
import {
  Module,
  HttpException,
  ArgumentsHost,
  Logger,
  Catch,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaService } from './services/prisma/prisma.service';
import { HealthController } from './health.controller';
import { UserManagementModule } from './services/user-management/user-management.module';
import { PrismaModule } from './services/prisma/prisma.module';
import { MasterFAQModule } from './services/master-faq/master-faq.module';
import { SliderManagementModule } from './services/slider-management/slider-management.module';
import { JobPositionModule } from './services/job-position/job-position.module';
import { MasterCategoryModule } from './services/master-category/master-category.module';
import { LogManagementModule } from './services/log-management/log-management.module';
import { LearningPathModule } from './services/learning-path/learning-path.module';
import { ManageMaterialModule } from './services/manage-material/manage-material.module';
import { QuestionBankModule } from './services/question-bank/question-bank.module';
import { ImageRepositoryModule } from './services/image-repository/image-repository.module';

@Catch(HttpException)
class HttpExceptionFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    if (exception instanceof ZodSerializationException) {
      const zodError = exception.getZodError();

      if (zodError instanceof ZodError) {
        this.logger.error(`ZodSerializationException: ${zodError.message}`);
      }
    }

    super.catch(exception, host);
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule,
    UserManagementModule,
    MasterFAQModule,
    SliderManagementModule,
    JobPositionModule,
    MasterCategoryModule,
    LogManagementModule,
    LearningPathModule,
    ManageMaterialModule,
    ImageRepositoryModule,
    QuestionBankModule,
  ],
  controllers: [AppController, HealthController],
  providers: [
    AppService,
    PrismaService,
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodSerializerInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
  exports: [PrismaService],
})
export class AppModule {}
