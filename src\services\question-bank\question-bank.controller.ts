import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { QuestionBankService } from './question-bank.service';
import { CGetQuestionBankListQueryDto } from './dto/list.dto';
import { CGetQuestionBankDetailParamsDto } from './dto/detail.dto';
import { FormDataGuard } from 'src/common/guards/form-data.guard';
import { FileUploadInterceptor } from 'src/common/interceptors/file-upload.interceptor';
import { CCreateQuestionBankBodyDto } from './dto/create.dto';
import {
  CUpdateQuestionBankBodyDto,
  CUpdateQuestionBankParamsDto,
} from './dto/update.dto';
import dayjs from 'dayjs';
import * as express from 'express';

@Controller('/admin/learning')
export class QuestionBankController {
  constructor(private readonly questionBankService: QuestionBankService) {}

  // Auth
  @Get('question-bank/list')
  getQuestionBankList(@Query() query: CGetQuestionBankListQueryDto) {
    return this.questionBankService.getQuestionBankList(query);
  }

  //   Auth
  @Get('question-bank/detail/:id')
  getQuestionBankDetail(@Param() params: CGetQuestionBankDetailParamsDto) {
    return this.questionBankService.getQuestionBankDetail(params);
  }

  // Auth
  @Get('question-bank/export')
  async exportQuestionBankList(
    @Query() query: CGetQuestionBankListQueryDto,
    @Res() res: express.Response,
  ) {
    const response =
      await this.questionBankService.exportQuestionBankList(query);

    const date = dayjs().format('DD-MM-YYYY');

    return res
      .set(
        'Content-Disposition',
        `attachment; filename=Lemon_question_bank_${date}.xlsx`,
      )
      .send(response);
  }

  // Auth
  @Post('question-bank/insert')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    allowedMimeTypes: /^image\/(jpg|jpeg|png|webp|svg)$/,
    maxSizeMB: 5,
  })
  async createQuestionBank(
    @Body() body: CCreateQuestionBankBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.questionBankService.createQuestionBank(body, file);
  }

  // Auth
  @Post('question-bank/update/:id')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    allowedMimeTypes: /^image\/(jpg|jpeg|png|webp|svg)$/,
    maxSizeMB: 5,
  })
  async updateQuestionBank(
    @Param() params: CUpdateQuestionBankParamsDto,
    @Body() body: CUpdateQuestionBankBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.questionBankService.updateQuestionBank(params, body, file);
  }
}
