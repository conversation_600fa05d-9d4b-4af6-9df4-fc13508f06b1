import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getCategoryDetailParams = z.object({ id: z.coerce.number() });

export class CGetCategoryDetailParamsDto extends createZodDto(
  getCategoryDetailParams,
) {}

export interface IGetCategoryDetailResponseDto {
  id: number;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
