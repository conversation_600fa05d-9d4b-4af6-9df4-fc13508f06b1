import { createZodDto } from 'nestjs-zod';
import { zBooleanOptional } from 'src/common/utils/validation.util';
import z from 'zod';

const updateCategorySchema = z.object({
  category_name: z.string().min(1, 'Category name cannot be empty'),
  is_deleted: zBooleanOptional(),
});

const updateCategoryParams = z.object({ id: z.coerce.number() });

export class CUpdateCategoryParamsDto extends createZodDto(
  updateCategoryParams,
) {}

export class CUpdateCategoryDto extends createZodDto(updateCategorySchema) {}
