import { PrismaService } from 'src/services/prisma/prisma.service';
import { CGetUserListQueryDto, IGetUserListResponseDto } from '../dto/list.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { handleError } from 'src/common/utils/error.util';

export const JGetUserList = async (
  prisma: PrismaService,
  query: CGetUserListQueryDto,
): Promise<IGlobalResponseDto<IGetUserListResponseDto[]>> => {
  try {
    const {
      page,
      entity_id,
      is_neop,
      is_new_user,
      role_id,
      search,
      search_by,
      type_id,
      limit,
    } = query;

    const filter: Prisma.user_detailsWhereInput = {
      is_deleted: false,
      entity_id,
      is_new_user: is_new_user,
      is_need_neop: is_neop,
      user_role_id: role_id,
      user_type_id: type_id,
    };

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.user_details.findMany({
        where: filter,
        select: {
          ID: true,
          name: true,
          job_name: true,
          email: true,
          second_email: true,
          phone_number: true,
          user_type_id: true,
          npk: true,
          user_type: {
            select: {
              user_type_name: true,
            },
          },
          user_role_id: true,
          roles: {
            select: {
              user_role_name: true,
            },
          },
          entity_id: true,
          entity: {
            select: {
              entity_name: true,
            },
          },
          is_need_neop: true,
          is_need_welcoming_kit: true,
          updated_at: true,
          updated_by: true,
          is_active: true,
          is_new_user: true,
        },
        orderBy: {
          updated_at: 'desc',
        },
        skip,
        take,
      }),
      prisma.user_details.count({
        where: filter,
      }),
    ]);

    const result: IGetUserListResponseDto[] = data.map((item) => ({
      id: item.ID,
      npk: item.npk,
      name: item.name,
      job_name: item.job_name,
      email: item.email,
      second_email: item.second_email,
      phone_number: item.phone_number,
      user_type_id: item.user_type_id,
      user_type_name: item.user_type?.user_type_name ?? '',
      user_role_id: item.user_role_id,
      user_role_name: item.roles?.user_role_name ?? '',
      entity_id: item.entity_id,
      entity_name: item.entity?.entity_name ?? '',
      is_need_neop: item.is_need_neop,
      is_need_welcoming_kit: item.is_need_welcoming_kit,
      updated_at: item.updated_at,
      updated_by: item.updated_by,
      is_active: item.is_active,
      is_new_user: item.is_new_user,
    }));

    return {
      status: true,
      message: 'success get data user',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/user-list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
