import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetCategoryDetailParamsDto,
  IGetCategoryDetailResponseDto,
} from '../dto/detail.dto';

export const JGetCategoryDetail = async (
  prisma: PrismaService,
  params: CGetCategoryDetailParamsDto,
): Promise<IGlobalResponseDto<IGetCategoryDetailResponseDto>> => {
  try {
    const { id } = params;

    const data = await prisma.category
      .findUniqueOrThrow({
        where: {
          id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        select: {
          id: true,
          category_name: true,
          created_at: true,
          created_by: true,
          last_updated: true,
          updated_by: true,
        },
      })
      .catch(() => {
        throw handleError({
          message: 'Category not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IGetCategoryDetailResponseDto = {
      id: data.id,
      category_name: data.category_name,
      created_by: data.created_by,
      updated_by: data.updated_by,
      created_at: data.created_at?.toISOString() ?? null,
      last_updated: data.last_updated?.toISOString() ?? null,
    };

    return {
      status: true,
      message: 'success get data category',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
