import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { Workbook } from 'exceljs';
import {
  CGetQuestionBankListQueryDto,
  IGetQuestionBankListResponseDto,
} from '../dto/list.dto';
import { JGetQuestionBankList } from './list.dto';

export const JExportQuestionBankList = async (
  prisma: PrismaService,
  query: CGetQuestionBankListQueryDto,
) => {
  try {
    const { data } = await JGetQuestionBankList(prisma, query);

    return await createFile(data);
  } catch (error: any) {
    throw handleError(error);
  }
};

const createFile = async (data: IGetQuestionBankListResponseDto[]) => {
  const workbook = new Workbook();

  const ws = workbook.addWorksheet();

  // 1) Definisikan columns (pakai header & key seperti semula)
  const columns = [
    { header: 'ID', key: 'id' },
    { header: 'Question', key: 'question' },
    { header: 'Type', key: 'type' },
    { header: 'Option A', key: 'option_a' },
    { header: 'Option B', key: 'option_b' },
    { header: 'Option C', key: 'option_c' },
    { header: 'Option D', key: 'option_d' },
    { header: 'Correct Answer', key: 'correct_answer' },
    { header: 'Correct Answer Percentage', key: 'correct_answer_percentage' },
    { header: 'Feature', key: 'feature' },
    { header: 'Created Date', key: 'created_at' },
    { header: 'Updated Date', key: 'last_updated' },
  ];

  ws.columns = columns;

  // 2) Kosongkan row-1 (exceljs menulis header di sini secara default)
  ws.getRow(1).values = new Array(columns.length + 1).fill(null);

  const titleCell = ws.getCell(1, 1);

  titleCell.alignment = { horizontal: 'center' };
  titleCell.font = { bold: true };

  // 3) Row 2: Header manual (bold)
  const headerRow = ws.getRow(2);
  columns.forEach((c, idx) => {
    const cell = headerRow.getCell(idx + 1);
    cell.value = c.header;
    cell.font = { bold: true };
    cell.alignment = { horizontal: 'center' };
  });
  headerRow.commit();

  // 4) Data mulai baris 3
  data.forEach((val) => ws.addRow(val));

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};
