import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

const getLogLoginListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z.enum(['npk']).optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetLogLoginListQueryDto extends createZodDto(
  getLogLoginListQuerySchema,
) {}

export interface IGetLogLoginListResponseDto {
  id: number | null;
  npk: string | null;
  start_date: string | null;
  end_date: string | null;
  duration: string | null;
  created_date: string | null;
  created_by: string | null;
  updated_date: string | null;
  updated_by: string | null;
}
