import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetUserDetailParamsDto,
  IGetUserDetailResponseDto,
} from '../dto/detail.dto';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetUserDetail = async (
  prisma: PrismaService,
  params: CGetUserDetailParamsDto,
): Promise<IGlobalResponseDto<IGetUserDetailResponseDto>> => {
  try {
    const { id } = params;

    const user = await prisma.user_details
      .findUniqueOrThrow({
        where: {
          ID: id,
          is_deleted: false,
        },
        select: {
          ID: true,
          npk: true,
          name: true,
          job_name_id: true,
          job_name: true,
          email: true,
          second_email: true,
          phone_number: true,
          supervisor_id: true,
          supervisor_name: true,
          user_type_id: true,
          user_role_id: true,
          entity_id: true,
          forum_title_id: true,
          is_need_neop: true,
          is_need_welcoming_kit: true,
          updated_at: true,
          updated_by: true,
          is_active: true,
          signature: true,
          user_type: {
            select: {
              user_type_name: true,
            },
          },
          roles: {
            select: {
              user_role_name: true,
            },
          },
          entity: {
            select: {
              entity_name: true,
            },
          },
          forum_title: true,
        },
      })
      .catch(() => {
        throw handleError({
          message: 'User not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IGetUserDetailResponseDto = {
      id: user.ID,
      npk: user.npk,
      name: user.name,
      job_name_id: user.job_name_id,
      job_name: user.job_name,
      email: user.email,
      second_email: user.second_email,
      phone_number: user.phone_number,
      supervisor_id: user.supervisor_id,
      supervisor_name: user.supervisor_name,
      user_type_id: user.user_type_id,
      user_type_name: user.user_type?.user_type_name ?? null,
      user_role_id: user.user_role_id,
      user_role_name: user.roles?.user_role_name ?? null,
      entity_id: user.entity_id,
      entity_name: user.entity?.entity_name ?? null,
      forum_title_id: user.forum_title_id,
      forum_title_name: user.forum_title,
      is_need_neop: user.is_need_neop,
      is_need_welcoming_kit: user.is_need_welcoming_kit,
      updated_at: user.updated_at,
      updated_by: user.updated_by,
      is_active: user.is_active,
      signature: user.signature,
    };

    return {
      status: true,
      message: 'success get data user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
