import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  convertImageToWebp,
  removeExtension,
} from 'src/common/utils/image-processor.util';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import { ISliderImage } from '../dto/create-slider.dto';
import {
  CUpdateSliderBodyDto,
  CUpdateSliderParamsDto,
} from '../dto/update.slider.dto';
import dayjs from 'dayjs';

export const JUpdateSlider = async (
  prisma: PrismaService,
  params: CUpdateSliderParamsDto,
  body: CUpdateSliderBodyDto,
  file: ISliderImage,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const data = body.data;

    let slider_desktop: string | undefined = undefined;
    let slider_mobile: string | undefined = undefined;

    if (
      file?.slider_desktop &&
      file.slider_desktop.mimetype.startsWith('image/')
    ) {
      slider_desktop = await uploadSliderImage(file.slider_desktop);
    }

    if (
      file?.slider_mobile &&
      file.slider_mobile.mimetype.startsWith('image/')
    ) {
      slider_mobile = await uploadSliderImage(file.slider_mobile);
    }

    await prisma.sliders.update({
      where: { ID: params.id },
      data: {
        slider_name: data.slider_name,
        link: data.link,
        slider: slider_desktop,
        slider_mobile: slider_mobile,
        is_deleted: data.is_deleted,
        updated_at: new Date(),
      },
    });

    return {
      status: true,
      message: 'success update data slider',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

async function uploadSliderImage(file: Express.Multer.File) {
  let buffer = file.buffer;
  let filePathName = file.originalname;
  const mimetype = 'image/webp';

  buffer = await convertImageToWebp(buffer);
  filePathName = `sliders/${dayjs().unix()}-${removeExtension(filePathName)}.webp`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': mimetype,
  });

  return `lemon/${filePathName}`;
}
