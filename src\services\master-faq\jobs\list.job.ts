import { PrismaService } from 'src/services/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { handleError } from 'src/common/utils/error.util';
import { CGetFAQListQueryDto, IGetFAQListResponseDto } from '../dto/list.dto';

export const JGetFAQList = async (
  prisma: PrismaService,
  query: CGetFAQListQueryDto,
): Promise<IGlobalResponseDto<IGetFAQListResponseDto[]>> => {
  try {
    const { search, search_by, page = 1, limit = 10 } = query;

    const filter: Prisma.faqWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
          },
        },
      ];
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.faq.findMany({
        where: filter,
        select: {
          ID: true,
          question: true,
          answer: true,
        },
        orderBy: {
          ID: 'asc',
        },
        skip,
        take,
      }),
      prisma.faq.count({
        where: filter,
      }),
    ]);

    const result: IGetFAQListResponseDto[] = data.map((item) => ({
      id: item.ID,
      question: item.question,
      answer: item.answer,
    }));

    return {
      status: true,
      message: 'success get data faq',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/faq-list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
