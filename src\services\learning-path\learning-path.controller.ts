import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { LearningPathService } from './learning-path.service';
import {
  CGetLearningCodeDetailParamsDto,
  CGetLearningCodeListQueryDto,
} from './dto/get-learning-code.dto';
import { CCreateLearningCodeBodyDto } from './dto/create-learning-code.dto';
import { CUpdateLearningCodeParamsDto } from './dto/update-learning-code.dto';
import {
  CUpdateLearningLevelBodyDto,
  CUpdateLearningLevelParamsDto,
} from './dto/update-learning-level.dto';
import { CCreateLearningLevelBodyDto } from './dto/create-learning-level.dto';
import {
  CGetLearningLevelDetailParamsDto,
  CGetLearningLevelListQueryDto,
} from './dto/get-learning-level.dto';
import { CGetCertificateListQueryDto } from './dto/get-certificate.dto';

@Controller('admin/learning')
export class LearningPathController {
  constructor(private readonly learningPathService: LearningPathService) {}

  @Get('code/list')
  async getLearningCodeList(@Query() query: CGetLearningCodeListQueryDto) {
    return this.learningPathService.getLearningCodeList(query);
  }

  @Get('code/detail/:id')
  async getLearningCodeDetail(
    @Param() params: CGetLearningCodeDetailParamsDto,
  ) {
    return this.learningPathService.getLearningCodeDetail(params);
  }

  @Post('code/insert')
  async createLearningCode(@Body() body: CCreateLearningCodeBodyDto) {
    return this.learningPathService.createLearningCode(body);
  }

  @Post('code/update/:id')
  async updateLearningCode(
    @Param() params: CUpdateLearningCodeParamsDto,
    @Body() body: CCreateLearningCodeBodyDto,
  ) {
    return this.learningPathService.updateLearningCode(params, body);
  }

  @Get('level/list')
  async getLearningLevelList(@Query() query: CGetLearningLevelListQueryDto) {
    return this.learningPathService.getLearningLevelList(query);
  }

  @Get('level/detail/:id')
  async getLearningLevelDetail(
    @Param() params: CGetLearningLevelDetailParamsDto,
  ) {
    return this.learningPathService.getLearningLevelDetail(params);
  }

  @Post('level/insert')
  async createLearningLevel(@Body() body: CCreateLearningLevelBodyDto) {
    return this.learningPathService.createLearningLevel(body);
  }

  @Post('level/update/:id')
  async updateLearningLevel(
    @Param() params: CUpdateLearningLevelParamsDto,
    @Body() body: CUpdateLearningLevelBodyDto,
  ) {
    return this.learningPathService.updateLearningLevel(params, body);
  }

  @Get('certificate/list')
  async getCertificateList(@Query() query: CGetCertificateListQueryDto) {
    return this.learningPathService.getCertificateList(query);
  }
}
