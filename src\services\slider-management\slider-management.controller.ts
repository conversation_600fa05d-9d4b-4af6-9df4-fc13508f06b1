import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  UploadedFiles,
  UseGuards,
} from '@nestjs/common';
import { SliderManagementService } from './slider-management.service';
import { CGetSliderDetailParamsDto } from './dto/detail-slider.dto';
import { FormDataGuard } from 'src/common/guards/form-data.guard';
import { MultiFileUploadInterceptor } from 'src/common/interceptors/file-upload.interceptor';
import { CCreateSliderBodyDto } from './dto/create-slider.dto';
import {
  CUpdateSliderBodyDto,
  CUpdateSliderParamsDto,
} from './dto/update.slider.dto';

@Controller('/admin')
export class SliderManagementController {
  constructor(
    private readonly sliderManagementService: SliderManagementService,
  ) {}

  // Auth
  @Get('slider-list')
  getFAQList() {
    return this.sliderManagementService.getSliderList();
  }

  // Auth
  @Get('slider-detail/:id')
  getFAQDetail(@Param() params: CGetSliderDetailParamsDto) {
    return this.sliderManagementService.getSliderDetail(params);
  }

  // Auth
  @Post('slider-insert')
  @UseGuards(FormDataGuard)
  @MultiFileUploadInterceptor({
    fields: [
      { name: 'slider_desktop', maxCount: 1 },
      { name: 'slider_mobile', maxCount: 1 },
    ],
    allowedMimeTypes: /^image\/(jpg|jpeg|png|gif|webp|bmp|tiff|heic|svg)$/,
    maxSizeMB: 10,
  })
  async createSlider(
    @Body() body: CCreateSliderBodyDto,
    @UploadedFiles()
    files?: {
      slider_desktop?: Express.Multer.File[];
      slider_mobile?: Express.Multer.File[];
    },
  ) {
    return this.sliderManagementService.createSlider(body, {
      slider_desktop: files?.slider_desktop?.[0],
      slider_mobile: files?.slider_mobile?.[0],
    });
  }

  // Auth
  @Post('slider-update/:id')
  @UseGuards(FormDataGuard)
  @MultiFileUploadInterceptor({
    fields: [
      { name: 'slider_desktop', maxCount: 1 },
      { name: 'slider_mobile', maxCount: 1 },
    ],
    allowedMimeTypes: /^image\/(jpg|jpeg|png|gif|webp|bmp|tiff|heic|svg)$/,
    maxSizeMB: 10,
  })
  async updateSlider(
    @Param() params: CUpdateSliderParamsDto,
    @Body() body: CUpdateSliderBodyDto,
    @UploadedFiles()
    files?: {
      slider_desktop?: Express.Multer.File[];
      slider_mobile?: Express.Multer.File[];
    },
  ) {
    return this.sliderManagementService.updateSlider(params, body, {
      slider_desktop: files?.slider_desktop?.[0],
      slider_mobile: files?.slider_mobile?.[0],
    });
  }
}
