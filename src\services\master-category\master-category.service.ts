import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CGetCategoryListQueryDto } from './dto/list.dto';
import { JGetCategoryList } from './jobs/list.job';
import { JGetCategoryDetail } from './jobs/detail.job';
import { CGetCategoryDetailParamsDto } from './dto/detail.dto';
import { CCreateCategoryDto } from './dto/create.dto';
import { JCreateCategory } from './jobs/create.job';
import { CUpdateCategoryDto, CUpdateCategoryParamsDto } from './dto/update.dto';
import { JUpdateCategory } from './jobs/update.job';
import { JExportCategoryList } from './jobs/export.job';
import { CExportCategoryQueryDto } from './dto/export.dto';
import { JCreateSubCategory } from './jobs/create-subcategory.job';
import { CCreateSubCategoryDto } from './dto/create-subcategory.dto';
import { CGetSubCategoryListQueryDto } from './dto/list-subcategory.dto';
import { JGetSubCategoryList } from './jobs/list-subcategory.job';
import { JGetSubCategoryDetail } from './jobs/detail-subcategory.job';
import { CGetSubCategoryDetailParamsDto } from './dto/detail-subcategory.dto';
import {
  CUpdateSubCategoryDto,
  CUpdateSubCategoryParamsDto,
} from './dto/update-subcategory.dto';
import { JUpdateSubCategory } from './jobs/update-subcategory.job';
import { CExportSubCategoryQueryDto } from './dto/export-subcategory.dto';
import { JExportSubCategoryList } from './jobs/export-subcategory.job';
import { CGetMasterCategoryQueryDto } from './dto/master-category.dto';
import { JGetMasterCategory } from './jobs/master-category.job';

@Injectable()
export class MasterCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  getCategoryList = (query: CGetCategoryListQueryDto) =>
    JGetCategoryList(this.prisma, query);
  getCategoryDetail = (params: CGetCategoryDetailParamsDto) =>
    JGetCategoryDetail(this.prisma, params);
  createCategory = (body: CCreateCategoryDto) =>
    JCreateCategory(this.prisma, body);
  updateCategory = (
    params: CUpdateCategoryParamsDto,
    body: CUpdateCategoryDto,
  ) => JUpdateCategory(this.prisma, params, body);
  getExportCategory = (query: CExportCategoryQueryDto) =>
    JExportCategoryList(this.prisma, query);
  getMasterCategory = (query: CGetMasterCategoryQueryDto) =>
    JGetMasterCategory(this.prisma, query);

  // sub category
  getSubCategoryList = (query: CGetSubCategoryListQueryDto) =>
    JGetSubCategoryList(this.prisma, query);
  getSubCategoryDetail = (params: CGetSubCategoryDetailParamsDto) =>
    JGetSubCategoryDetail(this.prisma, params);
  createSubCategory = (body: CCreateSubCategoryDto) =>
    JCreateSubCategory(this.prisma, body);
  updateSubCategory = (
    params: CUpdateSubCategoryParamsDto,
    body: CUpdateSubCategoryDto,
  ) => JUpdateSubCategory(this.prisma, params, body);
  getExportSubCategory = (query: CExportSubCategoryQueryDto) =>
    JExportSubCategoryList(this.prisma, query);
}
