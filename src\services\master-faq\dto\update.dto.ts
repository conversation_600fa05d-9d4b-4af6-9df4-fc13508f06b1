import { createZodDto } from 'nestjs-zod';
import z from 'zod';

export const updateFAQSchema = z.object({
  data: z.preprocess(
    (val) => {
      if (typeof val === 'string') {
        try {
          return JSON.parse(val);
        } catch {
          return val;
        }
      }
      return val;
    },
    z
      .object({
        question: z.string().optional(),
        answer: z.string().optional(),
        imgfaq: z.string().optional(),
        is_deleted: z
          .preprocess((val) => {
            if (typeof val === 'string') return val === 'true' || val === '1';
            if (typeof val === 'number') return val === 1;
            return val;
          }, z.boolean())
          .optional(),
        tags: z
          .any() // string/array
          .optional()
          .transform((val) => {
            if (!val) return [];

            // "[1,2,3]"
            if (typeof val === 'string') {
              try {
                const parsed = JSON.parse(val);
                if (Array.isArray(parsed)) return parsed.map(Number);
              } catch {
                // "1,2,3"
                return val.split(',').map((v) => Number(v.trim()));
              }
            }

            // ["1","2","3"]
            if (Array.isArray(val)) {
              return val.map((v) => Number(v));
            }

            return [];
          })
          .refine(
            (arr) => arr.every((n) => typeof n === 'number' && !isNaN(n)),
            {
              message: 'Tags must be numbers',
            },
          ),
      })
      .superRefine((data, ctx) => {
        if (data.is_deleted === undefined) {
          if (!data.question || data.question.trim() === '') {
            ctx.addIssue({
              path: ['question'],
              code: z.ZodIssueCode.custom,
              message: 'Question cannot be empty',
            });
          }
          if (!data.answer || data.answer.trim() === '') {
            ctx.addIssue({
              path: ['answer'],
              code: z.ZodIssueCode.custom,
              message: 'Answer cannot be empty',
            });
          }
        }
      }),
  ),
});

const updateFAQParams = z.object({ id: z.coerce.number() });

export class CUpdateFAQBodyDto extends createZodDto(updateFAQSchema) {}

export class CUpdateFAQParamsDto extends createZodDto(updateFAQParams) {}
