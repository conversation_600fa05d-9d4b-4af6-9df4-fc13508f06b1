import 'dotenv/config';

const env = {
  MINIO: {
    ENDPOINT: process.env.MINIO_ENDPOINT || '',
    PORT: isNaN(Number(process.env.MINIO_PORT))
      ? undefined
      : Number(process.env.MINIO_PORT),
    SSL: process.env.MINIO_SSL?.toString() === 'true' || false,
    ACCESS_KEY: process.env.MINIO_ACCESS_KEY || '',
    SECRET_KEY: process.env.MINIO_SECRET_KEY || '',
    BUCKETNAME: process.env.MINIO_BUCKETNAME || '',
  },
};

export default env;
