import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetUserEntityQueryDto,
  IGetUserEntityResponseDto,
} from '../dto/entity.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetUserEntity = async (
  prisma: PrismaService,
  query: CGetUserEntityQueryDto,
): Promise<IGlobalResponseDto<IGetUserEntityResponseDto[]>> => {
  try {
    const { search } = query;

    const filter: Prisma.entityWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search) {
      filter.AND = [
        {
          entity_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.entity.findMany({
      where: filter,
      select: {
        id: true,
        entity_name: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    if (!data.length) {
      return {
        status: true,
        message: 'no data found',
        data: [],
      };
    }

    const result: IGetUserEntityResponseDto[] = data.map((item) => ({
      entity_id: item.id,
      entity_name: item.entity_name,
    }));

    return {
      status: true,
      message: 'success get data entity user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
