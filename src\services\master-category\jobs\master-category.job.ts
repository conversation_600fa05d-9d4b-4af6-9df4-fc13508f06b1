import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetMasterCategoryQueryDto,
  IGetMasterCategoryResponseDto,
} from '../dto/master-category.dto';
import { Prisma } from '@prisma/client';

export const JGetMasterCategory = async (
  prisma: PrismaService,
  query: CGetMasterCategoryQueryDto,
): Promise<IGlobalResponseDto<IGetMasterCategoryResponseDto[] | object>> => {
  try {
    const { search } = query;

    const filter: Prisma.categoryWhereInput = {
      is_deleted: false,
    };

    // Add search filter if search term is provided
    if (search) {
      filter.category_name = {
        contains: String(search),
        mode: 'insensitive',
      };
    }

    const categories = await prisma.category.findMany({
      where: filter,
      select: {
        id: true,
        category_name: true,
      },
      orderBy: {
        category_name: 'asc',
      },
    });

    if (categories.length === 0) {
      return {
        status: false,
        message: 'data not found!',
        data: {},
      };
    }

    return {
      status: true,
      message: 'success get category',
      data: categories.map((cat) => ({
        id: cat.id,
        category_name: cat.category_name,
      })),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
