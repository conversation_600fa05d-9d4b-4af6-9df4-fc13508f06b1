import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import z from 'zod';

const getLearningCodeListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z
      .enum([
        'code',
        'name',
        'job_name',
        'modul_name',
        'created_by',
        'updated_by',
      ])
      .optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetLearningCodeListQueryDto extends createZodDto(
  getLearningCodeListQuerySchema,
) {}

export interface IGetLearningCodeListResponseDto {
  ID: number | null;
  code: string | null;
  name: string | null;
  related_job: { job_name_id: number | null; job_name: string | null }[];
  related_modul: { modul_id: number | null; modul_name: string | null }[];
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
  status: string | null;
}

// Learning code detail
const getLearningCodeDetailParamsSchema = z.object({
  id: z.coerce.number(),
});

export class CGetLearningCodeDetailParamsDto extends createZodDto(
  getLearningCodeDetailParamsSchema,
) {}

export interface IGetLearningCodeDetailResponseDto {
  ID: number | null;
  code: string | null;
  name: string | null;
  related_job: (number | null)[];
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
  status: string | null;
}
