import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

const getQuestionBankListQuerySchema = z
  .object({
    category_id: z.coerce.number().optional(),
    level_id: z.coerce.number().optional(),
    question_type: z.string().optional(),
    feature: z.enum(['OnlineLearning', 'InClassTraining']).optional(),
    search: z.union([z.string(), z.number()]).optional(),
    is_image: z.enum(['null', 'notnull']).optional(),
    search_by: z
      .enum([
        'question_id',
        'question',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'created_by',
        'updated_by',
      ])
      .optional(),
  })
  .refine(
    (data) => {
      if (data.search_by === 'question_id') {
        return (
          data.search === undefined ||
          typeof data.search === 'number' ||
          !isNaN(Number(data.search))
        );
      }
      return true;
    },
    {
      message: 'search must be number if search_by=question_id',
      path: ['search'],
    },
  )
  .transform((data) => {
    if (data.search_by === 'question_id' && typeof data.search === 'string') {
      return { ...data, search: Number(data.search) };
    }
    return data;
  })
  .and(globalPaginationQuerySchema);

export class CGetQuestionBankListQueryDto extends createZodDto(
  getQuestionBankListQuerySchema,
) {}

export interface IGetQuestionBankListResponseDto {
  id: number;
  question: string | null;
  type: string | null;
  option_a: string | null;
  option_b: string | null;
  option_c: string | null;
  option_d: string | null;
  correct_answer: string | null;
  correct_answer_percentage: number | null;
  feature: string | null;
  levels: {
    id: number | null;
    level: string | null;
  }[];
  categories: {
    id: number | null;
    name: string | null;
  }[];
  associated: {
    section_id: number | null;
    section_name: string | null;
  }[];
  created_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  last_updated: string | null;
}
