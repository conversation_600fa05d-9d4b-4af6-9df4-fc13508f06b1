import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { Prisma } from '@prisma/client';
import { Workbook } from 'exceljs';
import dayjs from 'dayjs';
import { CExportSubCategoryQueryDto } from '../dto/export-subcategory.dto';

interface ISubCategoryData {
  id: number;
  sub_category_name: string | null;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}

export const JExportSubCategoryList = async (
  prisma: PrismaService,
  query: CExportSubCategoryQueryDto,
) => {
  try {
    const { search } = query;

    const filter: Prisma.sub_categoryWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    let searchBy: any = query.search_by;

    if (searchBy === 'sub_category_id') searchBy = 'id';

    if (search && searchBy) {
      if (searchBy === 'category_name') {
        filter.AND = [
          {
            category: {
              category_name: {
                contains: search as string,
                mode: 'insensitive',
              },
            },
          },
        ];
      } else if (searchBy === 'id') {
        filter.AND = [
          {
            id: +search,
          },
        ];
      } else if (searchBy === 'created_at') {
        filter.AND = [
          {
            created_at: {
              gte: dayjs(search).startOf('day').toISOString(),
              lte: dayjs(search).endOf('day').toISOString(),
            },
          },
        ];
      } else {
        filter.AND = [
          {
            [searchBy]: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    const data = await prisma.sub_category.findMany({
      where: filter,
      select: {
        id: true,
        sub_category_name: true,
        category: true,
        created_at: true,
        created_by: true,
        last_updated: true,
        updated_by: true,
      },
      orderBy: {
        last_updated: 'desc',
      },
    });

    const result: ISubCategoryData[] = data.map((it) => {
      return {
        ...it,
        category_name: it.category?.category_name ?? null,
        created_at: it?.created_at
          ? dayjs(it.created_at).format('DD/MM/YYYY')
          : null,
        last_updated: it?.last_updated
          ? dayjs(it.last_updated).format('DD/MM/YYYY')
          : null,
      };
    });

    return await createFile(result);
  } catch (error: any) {
    throw handleError(error);
  }
};

const createFile = async (data: ISubCategoryData[]) => {
  const workbook = new Workbook();

  const ws = workbook.addWorksheet();

  // 1) Definisikan columns (pakai header & key seperti semula)
  const columns = [
    { header: 'Sub Category ID', key: 'id' },
    { header: 'Sub Category Name', key: 'sub_category_name' },
    { header: 'Category Name', key: 'category_name' },
    { header: 'Created At', key: 'created_at' },
    { header: 'Created By', key: 'created_by' },
    { header: 'Last Updated', key: 'last_updated' },
    { header: 'Updated By', key: 'updated_by' },
  ];

  ws.columns = columns;

  // 2) Kosongkan row-1 (exceljs menulis header di sini secara default)
  ws.getRow(1).values = new Array(columns.length + 1).fill(null);

  const titleCell = ws.getCell(1, 1);

  titleCell.alignment = { horizontal: 'center' };
  titleCell.font = { bold: true };

  // 3) Row 2: Header manual (bold)
  const headerRow = ws.getRow(2);
  columns.forEach((c, idx) => {
    const cell = headerRow.getCell(idx + 1);
    cell.value = c.header;
    cell.font = { bold: true };
    cell.alignment = { horizontal: 'center' };
  });
  headerRow.commit();

  // 4) Data mulai baris 3
  data.forEach((val) => ws.addRow(val));

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};
