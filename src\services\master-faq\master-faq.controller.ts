import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { MasterFAQService } from './master-faq.service';
import { CGetFAQListQueryDto } from './dto/list.dto';
import { CGetFAQDetailParamsDto } from './dto/detail.dto';
import { CCreateFAQBodyDto } from './dto/create.dto';
import { FormDataGuard } from 'src/common/guards/form-data.guard';
import { FileUploadInterceptor } from 'src/common/interceptors/file-upload.interceptor';
import { CUpdateFAQBodyDto, CUpdateFAQParamsDto } from './dto/update.dto';
import { CGetTagListQueryDto } from './dto/list-tag.dto';

@Controller('/admin')
export class MasterFAQController {
  constructor(private readonly masterFAQService: MasterFAQService) {}

  // Auth
  @Get('faq-list')
  getFAQList(@Query() query: CGetFAQListQueryDto) {
    return this.masterFAQService.getFAQList(query);
  }

  // Auth
  @Get('faq-detail/:id')
  getFAQDetail(@Param() params: CGetFAQDetailParamsDto) {
    return this.masterFAQService.getFAQDetail(params);
  }

  // Auth
  @Post('faq-insert')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    maxSizeMB: 10,
    allowedMimeTypes: /^image\/(jpg|jpeg|png|gif|webp|bmp|tiff|heic|svg)$/,
  })
  async createFAQ(
    @Body() body: CCreateFAQBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.masterFAQService.createFAQ(body, file);
  }

  // Auth
  @Post('faq-update/:id')
  @UseGuards(FormDataGuard)
  @FileUploadInterceptor({
    fieldName: 'file',
    maxSizeMB: 10,
    allowedMimeTypes: /^image\/(jpg|jpeg|png|gif|webp|bmp|tiff|heic|svg)$/,
  })
  async updateFAQ(
    @Param() params: CUpdateFAQParamsDto,
    @Body() body: CUpdateFAQBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.masterFAQService.updateFAQ(params, body, file);
  }

  // Auth
  @Get('master/tags')
  getTagList(@Query() query: CGetTagListQueryDto) {
    return this.masterFAQService.getTagList(query);
  }
}
