import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CGetQuestionBankListQueryDto } from './dto/list.dto';
import { JGetQuestionBankList } from './jobs/list.dto';
import { CGetQuestionBankDetailParamsDto } from './dto/detail.dto';
import { JGetQuestionBankDetail } from './jobs/detail.job';
import { CCreateQuestionBankBodyDto } from './dto/create.dto';
import { JCreateQuestionBank } from './jobs/create.job';
import {
  CUpdateQuestionBankBodyDto,
  CUpdateQuestionBankParamsDto,
} from './dto/update.dto';
import { JUpdateQuestionBank } from './jobs/update.dto';
import { JExportQuestionBankList } from './jobs/export-excel.job';

@Injectable()
export class QuestionBankService {
  constructor(private readonly prisma: PrismaService) {}

  getQuestionBankList = (query: CGetQuestionBankListQueryDto) =>
    JGetQuestionBankList(this.prisma, query);
  getQuestionBankDetail = (params: CGetQuestionBankDetailParamsDto) =>
    JGetQuestionBankDetail(this.prisma, params);
  exportQuestionBankList = (query: CGetQuestionBankListQueryDto) =>
    JExportQuestionBankList(this.prisma, query);
  createQuestionBank = (
    body: CCreateQuestionBankBodyDto,
    file?: Express.Multer.File,
  ) => JCreateQuestionBank(this.prisma, body, file);
  updateQuestionBank = (
    params: CUpdateQuestionBankParamsDto,
    body: CUpdateQuestionBankBodyDto,
    file?: Express.Multer.File,
  ) => JUpdateQuestionBank(this.prisma, params, body, file);
}
