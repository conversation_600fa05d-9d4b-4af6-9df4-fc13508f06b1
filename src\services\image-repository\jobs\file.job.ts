import { handleError } from 'src/common/utils/error.util';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import dayjs from 'dayjs';
import {
  convertImageToWebp,
  removeExtension,
} from 'src/common/utils/image-processor.util';

export async function validateFile(file?: Express.Multer.File) {
  if (!file) {
    throw handleError({
      status: 400,
      message: 'file is required',
    });
  }
}

export async function uploadToMinio(
  file: Express.Multer.File,
  imgName?: string,
) {
  let buffer = file.buffer;
  let filePathName = file.originalname;
  const mimetype = 'image/webp';

  buffer = await convertImageToWebp(buffer);

  const fileName = `${dayjs().unix()}-${imgName ?? removeExtension(filePathName)}.webp`;
  filePathName = `materials/image/${fileName}`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': mimetype,
  });

  return {
    filePathName,
    fileName,
  };
}
