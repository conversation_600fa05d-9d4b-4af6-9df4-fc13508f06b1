import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetForumTitleQueryDto,
  IGetForumTitleResponseDto,
} from '../dto/forum-title.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetForumTitle = async (
  prisma: PrismaService,
  query: CGetForumTitleQueryDto,
): Promise<IGlobalResponseDto<IGetForumTitleResponseDto[]>> => {
  try {
    const { search } = query;

    const filter: Prisma.forum_titleWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search) {
      filter.AND = [
        {
          forum_title_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.forum_title.findMany({
      where: filter,
      select: {
        id: true,
        forum_title_name: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    if (!data.length) {
      return {
        status: true,
        message: 'no data found',
        data: [],
      };
    }

    const result: IGetForumTitleResponseDto[] = data.map((item) => ({
      forum_id: item.id,
      forum_name: item.forum_title_name,
    }));

    return {
      status: true,
      message: 'success get data forum user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
