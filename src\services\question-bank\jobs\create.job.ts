import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { CCreateQuestionBankBodyDto } from '../dto/create.dto';
import { JCreateImageRepository } from 'src/services/image-repository/jobs/create.job';

export const JCreateQuestionBank = async (
  prisma: PrismaService,
  body: CCreateQuestionBankBodyDto,
  file?: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const data = body.data;

    let imageId = data?.image_id ?? null;

    // insert into image repository
    if (file) {
      const imgRepository = await JCreateImageRepository(
        prisma,
        {
          data: {
            feature: data.feature,
            image_name: data.image_name,
            category_id: [],
            level_id: [],
          },
        },
        file,
        true,
      );

      if (imgRepository.data?.imageId) {
        imageId = imgRepository.data.imageId;
      }
    }

    const question = await prisma.question.create({
      data: {
        question: data.question,
        type: data.type,
        a: data.option_a,
        b: data.option_b,
        c: data.option_c,
        d: data.option_d,
        correct_answer: data.correct_answer,
        correct_answer_percentage: null,
        feature: data.feature,
        image_id: imageId,
        is_deleted: false,
        created_at: new Date(),
        created_by: null, // logged in username
        last_updated: new Date(),
        updated_by: null, // logged in username
      },
      select: { id: true },
    });

    if (data.level_id?.length) {
      const levels = data.level_id.map((id) => ({
        question_id: question.id,
        levels_id: id,
      }));

      await prisma.question_level_mapping.createMany({
        data: levels,
      });
    }

    if (data.category_id?.length) {
      const categories = data.category_id.map((id) => ({
        question_id: question.id,
        category_id: id,
      }));

      await prisma.question_category_mapping.createMany({
        data: categories,
      });
    }

    return {
      status: true,
      message: 'success insert question bank',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
