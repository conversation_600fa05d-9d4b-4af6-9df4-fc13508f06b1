generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model accpedia_information {
  id                                                                                                     Int                            @id(map: "accpedia_information_pk") @default(autoincrement())
  title                                                                                                  String?                        @db.VarChar
  key_word                                                                                               String?                        @db.VarChar
  description                                                                                            String?                        @db.VarChar
  application_example                                                                                    String?                        @db.VarChar
  formula                                                                                                String?                        @db.VarChar
  reference                                                                                              String?                        @db.VarChar
  status                                                                                                 String?                        @db.VarChar
  creator_id                                                                                             Int?
  created_at                                                                                             DateTime?                      @db.Timestamp(6)
  created_by                                                                                             String?                        @db.VarChar
  last_updated                                                                                           DateTime?                      @db.Timestamp(6)
  updated_by                                                                                             String?                        @db.VarChar
  is_deleted                                                                                             Boolean?
  total_view                                                                                             Int?
  suggested_keyword                                                                                      Boolean?
  original_id                                                                                            Int?
  is_active                                                                                              Boolean?
  user_details                                                                                           user_details?                  @relation(fields: [creator_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "accpedia_information_fk")
  accpedia_related_information_accpedia_related_information_information_idToaccpedia_information         accpedia_related_information[] @relation("accpedia_related_information_information_idToaccpedia_information")
  accpedia_related_information_accpedia_related_information_related_information_idToaccpedia_information accpedia_related_information[] @relation("accpedia_related_information_related_information_idToaccpedia_information")
}

model accpedia_related_information {
  id                                                                                             Int                   @id(map: "accpedia_related_information_pk") @default(autoincrement())
  information_id                                                                                 Int?
  related_information_id                                                                         Int?
  accpedia_information_accpedia_related_information_information_idToaccpedia_information         accpedia_information? @relation("accpedia_related_information_information_idToaccpedia_information", fields: [information_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "accpedia_related_information_fk")
  accpedia_information_accpedia_related_information_related_information_idToaccpedia_information accpedia_information? @relation("accpedia_related_information_related_information_idToaccpedia_information", fields: [related_information_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "accpedia_related_information_fk_1")
}

model alc_external_task {
  ID                   Int                @id(map: "alc_external_task_pk") @default(autoincrement())
  name                 String?            @db.VarChar
  url                  String?            @db.VarChar
  alc_matrix_detail_id Int?
  alc_matrix_detail    alc_matrix_detail? @relation(fields: [alc_matrix_detail_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "alc_external_task_fk")
}

model alc_matrix {
  ID            Int           @id(map: "alc_matrix_pk") @default(autoincrement())
  job_name_id   Int?
  competency_id Int?
  requirement   Float?        @db.Real
  status        String?       @db.VarChar
  competencies  competencies? @relation(fields: [competency_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alc_matrix_fk")
  job_names     job_names?    @relation(fields: [job_name_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "alc_matrix_fk_1")
}

model alc_matrix_detail {
  ID                Int                 @id(map: "alc_matrix_detail_pk") @default(autoincrement())
  competency_id     Int?
  requirement       Float?              @db.Real
  status            String?             @db.VarChar
  created_date      DateTime?           @db.Timestamp(6)
  updated_date      DateTime?           @db.Timestamp(6)
  description       String?
  assignment_status Boolean?
  alc_external_task alc_external_task[]
  competencies      competencies?       @relation(fields: [competency_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alc_matrix_detail_fk")
}

model alc_result {
  id               BigInt        @id(map: "alc_result_pk") @default(autoincrement())
  npk              String?       @db.VarChar
  competency_id    Int?
  individual_score Float?        @db.Real
  people_review    Float?        @db.Real
  status           String?       @db.VarChar
  created_date     DateTime?     @db.Timestamptz(6)
  updated_date     DateTime?     @db.Timestamp(6)
  job_name_id      Int?
  username         String?       @db.VarChar
  period           Int?
  competencies     competencies? @relation(fields: [competency_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alc_result_fk")
  job_names        job_names?    @relation(fields: [job_name_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "alc_result_fk_1")
}

model api_key {
  id       Int     @id(map: "api_key_pk") @default(autoincrement())
  app_name String? @db.VarChar
  api_key  String? @db.VarChar
}

model api_log {
  id         Int       @id(map: "newtable_pk") @default(autoincrement())
  domain     String?   @db.VarChar
  parameter  String?
  created_at DateTime? @db.Timestamp(6)
}

model career_path {
  ID           Int       @id(map: "career_path_pk") @default(autoincrement())
  origin       Int?
  destination  Int?
  type         String?   @db.VarChar
  created_date DateTime? @db.Timestamp(6)
  updated_date DateTime? @db.Timestamp(6)
  status       String?   @db.VarChar
  job_path     job_path? @relation(fields: [origin], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "career_path_fk")
}

model career_plan {
  id           BigInt        @id(map: "career_plan_pk") @default(autoincrement())
  npk          String?       @db.VarChar
  job_path_id  Int?
  order        Int?
  created_at   DateTime?     @db.Timestamp(6)
  jobname      String?       @db.VarChar
  name         String?       @db.VarChar
  user_id      Int?
  job_path     job_path?     @relation(fields: [job_path_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "career_plan_fk")
  user_details user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "career_plan_fk_1")
}

model category {
  id                                 Int                                  @id(map: "category_pk") @default(autoincrement())
  category_name                      String?                              @db.VarChar
  is_deleted                         Boolean?
  last_updated                       DateTime?                            @db.Timestamp(6)
  updated_by                         String?                              @db.VarChar
  created_at                         DateTime?                            @db.Timestamp(6)
  created_by                         String?                              @db.VarChar
  image_category_mapping             image_category_mapping[]
  inclass_sections                   inclass_sections[]
  material_category_mapping          material_category_mapping[]
  modul_category_mapping             modul_category_mapping[]
  question_category_mapping          question_category_mapping[]
  question_template_category_mapping question_template_category_mapping[]
  sub_category                       sub_category[]
  sub_section_category_mapping       sub_section_category_mapping[]
}

model certification_background {
  id                            Int                @id(map: "certification_background_pk") @default(autoincrement())
  certification_background_name String?            @db.VarChar
  link                          String?            @db.VarChar
  preview_link                  String?            @db.VarChar
  inclass_training              inclass_training[]
  moduls                        moduls[]
}

model comment_likes {
  ID         Int     @id(map: "comment_likes_pk")
  npk        String? @db.VarChar
  comment_id Int?
  state      Int?
}

model comments {
  npk          String   @db.VarChar
  thread_id    Int
  comment      String
  created_date DateTime @db.Timestamp(6)
  parent__id   Int?
  ID           Int      @id(map: "comments_pk") @default(autoincrement())
  thread       thread   @relation(fields: [thread_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "comments_fk")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model competencies {
  id                                 Int                                  @id(map: "competencies_pk") @default(autoincrement())
  name                               String?                              @db.VarChar
  type                               String?                              @db.VarChar
  level_min                          Int?
  level_max                          Int?
  code                               String?                              @db.VarChar
  alc_matrix                         alc_matrix[]
  alc_matrix_detail                  alc_matrix_detail[]
  alc_result                         alc_result[]
  competencies_section               competencies_section[]
  tecat_matrix_detail                tecat_matrix_detail[]
  tugas_competencies_aspirasi_detail tugas_competencies_aspirasi_detail[]

  @@unique([code, type], map: "competencies_un")
}

model competencies_section {
  id              Int           @id(map: "competencies_modul_pk") @default(autoincrement())
  competencies_id Int?
  section_id      Int?
  level           Int?
  requirement     Float?        @db.Real
  competencies    competencies? @relation(fields: [competencies_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "competencies_section_fk")
  sub_section     sub_section?  @relation(fields: [section_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "competencies_section_subsection_fk")
}

model content_image {
  id                 Int     @id(map: "content_image_pk") @default(autoincrement())
  content_image_name String? @db.VarChar
  link               String? @db.VarChar
}

model copywriting {
  ID         Int     @id(map: "copywriting_pk")
  name       String? @db.VarChar
  content_ID String?
  content_EN String?
}

model department_branchgrade {
  id                Int          @id(map: "department_branchgrade_pk") @default(autoincrement())
  department_id     String?      @db.VarChar
  job_function_name String?      @db.VarChar
  branch_grade      String?      @db.VarChar
  departments       departments? @relation(fields: [department_id], references: [department_id], onDelete: NoAction, onUpdate: NoAction, map: "department_branchgrade_fk")

  @@unique([department_id, job_function_name], map: "department_branchgrade_un")
}

model departments {
  department_id          String                   @id(map: "departments_pk") @db.VarChar
  department_name        String?                  @db.VarChar
  branch_grade           String?                  @db.VarChar
  department_branchgrade department_branchgrade[]
  job_names              job_names[]
}

model digilib_categories {
  id   Int     @id(map: "digilib_categories_pk") @default(autoincrement())
  name String? @db.VarChar
}

model email_sender {
  id         Int       @id(map: "email_sender_pk") @default(autoincrement())
  cc         String?   @db.VarChar
  bcc        String?   @db.VarChar
  subject    String?   @db.VarChar
  body       String?   @db.VarChar
  created_at DateTime? @db.Timestamp(6)
  email_to   String?   @db.VarChar
}

model email_template {
  id      Int     @id(map: "email_template_pk") @default(autoincrement())
  feature String? @db.VarChar
  cc      String? @db.VarChar
  bcc     String? @db.VarChar
  subject String? @db.VarChar
  body    String? @db.VarChar
}

model entity {
  id                    Int                     @id(map: "entity_pk") @default(autoincrement())
  entity_name           String?                 @db.VarChar
  is_deleted            Boolean?
  last_updated          DateTime?               @db.Timestamp(6)
  updated_by            String?                 @db.VarChar
  created_at            DateTime?               @db.Timestamp(6)
  created_by            String?                 @db.VarChar
  job_names             job_names[]
  moduls                moduls[]
  moduls_entity_mapping moduls_entity_mapping[]
  user_details          user_details[]
}

model events {
  id                          BigInt                        @id(map: "events_pk") @default(autoincrement())
  title                       String?                       @db.VarChar
  description                 String?
  prerequisite                String?
  location                    String?                       @db.VarChar
  date                        DateTime?                     @db.Date
  starthour                   String?                       @db.VarChar(5)
  endhour                     String?                       @db.VarChar(5)
  days                        Int?
  reminder                    String?                       @db.VarChar(10)
  batch                       Int?
  inclass_training_id         Int?
  last_updated                DateTime?                     @db.Timestamp(6)
  updated_by                  String?                       @db.VarChar
  created_at                  DateTime?                     @db.Timestamp(6)
  created_by                  String?                       @db.VarChar
  is_deleted                  Boolean?
  inclass_training            inclass_training?             @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "events_fk")
  events_jobname              events_jobname[]
  events_npk                  events_npk[]
  events_participant          events_participant[]
  inclass_certificate         inclass_certificate[]
  inclass_score               inclass_score[]
  inclass_training_attendance inclass_training_attendance[]
  inclass_training_progress   inclass_training_progress[]
  inclass_user_assignment     inclass_user_assignment[]
  inclass_user_test_answers   inclass_user_test_answers[]
  inclass_user_test_result    inclass_user_test_result[]
}

model events_jobname {
  id         BigInt     @id(map: "events_jobname_pk") @default(autoincrement())
  event_id   BigInt?
  jobname_id Int?
  events     events?    @relation(fields: [event_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "events_jobname_fk")
  job_names  job_names? @relation(fields: [jobname_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "events_jobname_fk_1")
}

model events_npk {
  id           BigInt        @id(map: "events_npk_pk") @default(autoincrement())
  event_id     BigInt?
  npk          String?       @db.VarChar
  name         String?       @db.VarChar
  jobname      String?       @db.VarChar
  user_id      Int?
  user_details user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "events_fk_1")
  events       events?       @relation(fields: [event_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "events_npk_fk")
}

model events_participant {
  id                BigInt        @id(map: "events_participant_pk") @default(autoincrement())
  event_id          BigInt?
  npk               String?       @db.VarChar
  name              String?       @db.VarChar
  registration_date DateTime?     @db.Timestamp(6)
  manager_approved  Boolean?
  admin_approved    Boolean?
  point             Float?        @db.Real
  jobname           String?       @db.VarChar
  user_id           Int?
  last_updated      DateTime?     @db.Timestamp(6)
  updated_by        String?       @db.VarChar
  events            events?       @relation(fields: [event_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "events_participant_fk")
  user_details      user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "events_participant_fk1")

  @@unique([event_id, npk], map: "events_participant_un")
}

model external_training {
  ID             Int       @id(map: "external_training_pk") @default(autoincrement())
  npk            String?   @db.VarChar
  username       String?   @db.VarChar
  job_name       String?   @db.VarChar
  type           String?   @db.VarChar
  training_name  String?   @db.VarChar
  institution    String?   @db.VarChar
  year           Int?
  duration       Int?
  date_started   DateTime? @db.Timestamp(6)
  date_completed DateTime? @db.Timestamp(6)
  certificate    String?   @db.VarChar
  created_date   DateTime? @db.Timestamp(6)
  updated_date   DateTime? @db.Timestamp(6)

  @@unique([npk, training_name, date_started, institution], map: "external_training_un")
}

model faq {
  ID         Int       @id(map: "faq_pk") @default(autoincrement())
  question   String?   @db.VarChar
  answer     String?
  imgFaq     String?   @db.VarChar
  likes      Int?      @default(0)
  is_deleted Boolean?  @default(false) @db.Boolean
  created_by String?   @db.VarChar
  created_at DateTime? @default(now()) @db.Timestamp(6)
  updated_by String?   @db.VarChar
  updated_at DateTime? @db.Timestamp(6)

  faq_tags faq_tags[]
}

model faq_tags {
  ID      Int   @id(map: "faq_tags_pk") @default(autoincrement())
  faq_id  Int?
  tags_id Int?
  faq     faq?  @relation(fields: [faq_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "faq_tags_fk")
  tags    tags? @relation(fields: [tags_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "faq_tags_tag_fk")
}

model forum_comment {
  id           Int       @id(map: "forum_comment_pk") @default(autoincrement())
  comment      String    @db.VarChar
  type         String    @db.VarChar
  reference    Int
  status       String?   @db.VarChar
  creator_npk  String?   @db.VarChar
  created_at   DateTime  @default(now()) @db.Timestamptz(6)
  creator_name String?   @db.VarChar
  mention      String?   @db.VarChar
  updated_at   DateTime? @db.Timestamptz(6)

  @@index([reference])
}

model forum_post {
  id           Int         @id(map: "forum_post_pk") @default(autoincrement())
  title        String      @db.VarChar
  content      String
  is_trending  Boolean?    @default(false)
  status       String?     @db.VarChar
  creator_name String?     @db.VarChar
  creator_npk  String      @db.VarChar
  topic_id     Int
  created_at   DateTime    @default(now()) @db.Timestamp(6)
  creator_job  String?     @db.VarChar
  updated_at   DateTime?   @db.Timestamp(6)
  forum_topic  forum_topic @relation(fields: [topic_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "forum_post_fk")
}

model forum_reaction {
  id          Int       @id(map: "forum_reaction_pk") @default(autoincrement())
  reaction    String    @db.VarChar
  type        String    @db.VarChar
  reference   Int
  creator_npk String    @db.VarChar
  created_at  DateTime  @default(now()) @db.Timestamp(6)
  updated_at  DateTime? @db.Timestamp(6)
}

model forum_title {
  id               Int            @id(map: "forum_title_pk") @default(autoincrement())
  forum_title_name String?        @db.VarChar
  is_deleted       Boolean?
  last_updated     DateTime?      @db.Timestamp(6)
  updated_by       String?        @db.VarChar
  created_at       DateTime?      @db.Timestamp(6)
  created_by       String?        @db.VarChar
  user_details     user_details[]
}

model forum_topic {
  id            Int          @id(map: "forum_topic_pk") @default(autoincrement())
  name          String       @db.VarChar
  description   String       @db.VarChar
  is_main_topic Boolean?
  status        String?      @db.VarChar
  created_at    DateTime     @default(now()) @db.Timestamp(6)
  updated_at    DateTime     @default(now()) @db.Timestamp(6)
  forum_post    forum_post[]
}

model forum_user_report {
  id          Int       @id(map: "forum_user_report_pk") @default(autoincrement())
  type        String    @db.VarChar
  reference   Int
  creator_npk String?   @db.VarChar
  created_at  DateTime  @default(now()) @db.Timestamp(6)
  updated_at  DateTime? @db.Timestamp(6)
}

model image_category_mapping {
  id                  Int               @id(map: "image_category_mapping_pk") @default(autoincrement())
  image_repository_id Int?
  category_id         Int?
  image_repository    image_repository? @relation(fields: [image_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "image_category_mapping_fk")
  category            category?         @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "image_category_mapping_fk_1")
}

model image_level_mapping {
  id                  Int               @id(map: "image_level_mapping_pk") @default(autoincrement())
  image_repository_id Int?
  levels_id           Int?
  image_repository    image_repository? @relation(fields: [image_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "image_level_mapping_fk")
  levels              levels?           @relation(fields: [levels_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "image_level_mapping_fk_1")
}

model image_repository {
  id                     Int                      @id(map: "image_repository_pk") @default(autoincrement())
  image_name             String?                  @db.VarChar
  file_format            String?                  @db.VarChar
  file_size              Int?
  link                   String?                  @db.VarChar
  is_deleted             Boolean?
  last_updated           DateTime?                @db.Timestamp(6)
  updated_by             String?                  @db.VarChar
  created_at             DateTime?                @db.Timestamp(6)
  created_by             String?                  @db.VarChar
  feature                String?                  @db.VarChar
  image_category_mapping image_category_mapping[]
  image_level_mapping    image_level_mapping[]
  question               question[]
}

model inclass_category {
  id                  Int                   @id(map: "inclass_category_pk") @default(autoincrement())
  name                String?               @unique(map: "inclass_category_un") @db.VarChar
  status              String?               @db.VarChar
  inclass_subcategory inclass_subcategory[]
  inclass_test        inclass_test[]
}

model inclass_certificate {
  id                  Int               @id(map: "inclass_certificate_pk") @default(autoincrement())
  user_id             Int?
  event_id            BigInt?
  inclass_training_id Int?
  issued_date         DateTime?         @db.Timestamp(6)
  expired_date        DateTime?         @db.Timestamp(6)
  status              String?           @db.VarChar
  link                String?           @db.VarChar
  user_details        user_details?     @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_certificate_fk")
  inclass_training    inclass_training? @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_certificate_fk_1")
  events              events?           @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_certificate_fk_2")
}

model inclass_files {
  id         Int       @id(map: "inclass_files_pk") @default(autoincrement())
  name       String?   @db.VarChar
  size       Float?    @db.Real
  path       String?   @db.VarChar
  created_at DateTime? @db.Timestamp(6)
  updated_at DateTime? @db.Timestamp(6)
  status     String?   @db.VarChar
  type       String?   @db.VarChar
}

model inclass_levels {
  id           Int            @id(map: "inclass_levels_pk") @default(autoincrement())
  name         String?        @unique(map: "inclass_levels_un") @db.VarChar
  status       String?        @db.VarChar
  inclass_test inclass_test[]
}

model inclass_score {
  id                   Int               @id(map: "inclass_score_pk") @default(autoincrement())
  event_participant_id Int
  npk                  String?           @db.VarChar
  user_id              Int?
  event_id             BigInt?
  inclass_training_id  Int?
  attendance           BigInt?
  pretest_score        Int?
  posttest_pg_score    Int?
  posttest_isian_score Int?
  secondarytest_score  Int?
  posttest_score       Int?
  final_score          Int?
  updated_at           DateTime?         @db.Timestamp(6)
  events               events?           @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_score_events_fk")
  inclass_training     inclass_training? @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_score_fk")
  user_details         user_details?     @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_score_user_details_fk")
}

model inclass_section_mapping {
  id                  Int               @id(map: "inclass_section_mapping_pk") @default(autoincrement())
  inclass_training_id Int?
  inclass_section_id  Int?
  created_at          DateTime?         @db.Timestamp(6)
  updated_at          DateTime?         @db.Timestamp(6)
  inclass_trainer_id  Int?
  start_date          DateTime?         @db.Date
  start_time          String?           @db.VarChar(5)
  end_time            String?           @db.VarChar(5)
  trainer_name        String?           @db.VarChar
  type                String?           @db.VarChar
  inclass_training    inclass_training? @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_section_mapping_fk")
  inclass_sections    inclass_sections? @relation(fields: [inclass_section_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_section_mapping_fk_1")
  user_details        user_details?     @relation(fields: [inclass_trainer_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_section_mapping_fk_2")

  @@unique([inclass_training_id, inclass_section_id, inclass_trainer_id], map: "inclass_section_mapping_un")
}

model inclass_sections {
  id                        Int                         @id(map: "inclass_sections_pk") @default(autoincrement())
  name                      String?                     @db.VarChar
  type                      String?                     @db.VarChar
  inclass_file_id           Int?
  inclass_category_id       Int?
  created_at                DateTime?                   @db.Timestamp(6)
  updated_at                DateTime?                   @db.Timestamp(6)
  status                    String?                     @db.VarChar
  external_link             String?                     @db.VarChar
  inclass_subcategory_id    Int?
  materials_repository_id   Int?
  updated_by                String?                     @db.VarChar
  created_by                String?                     @db.VarChar
  passing_grade             Int?
  number_of_question        Int?
  is_deleted                Boolean?
  assignment_instruction    String?                     @db.VarChar
  with_test_timer           Boolean?
  is_retake_posttest        Boolean?
  inclass_section_mapping   inclass_section_mapping[]
  materials_repository      materials_repository?       @relation(fields: [materials_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_sections_fk")
  category                  category?                   @relation(fields: [inclass_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_sections_fk_1")
  sub_category              sub_category?               @relation(fields: [inclass_subcategory_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_sections_fk_2")
  inclass_training_progress inclass_training_progress[]
  inclass_user_assignment   inclass_user_assignment[]
  inclass_user_test_answers inclass_user_test_answers[]
  inclass_user_test_result  inclass_user_test_result[]
  test_question_mapping     test_question_mapping[]
}

model inclass_subcategory {
  id                  Int               @id(map: "inclass_subcategory_pk") @default(autoincrement())
  name                String?           @db.VarChar
  status              String?           @db.VarChar
  inclass_category_id Int?
  inclass_category    inclass_category? @relation(fields: [inclass_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_subcategory_fk")
}

model inclass_test {
  id                   Int                    @id(map: "inclass_test_pk") @default(autoincrement())
  question             String?
  correct_answer       String?                @db.VarChar
  a                    String?                @db.VarChar
  b                    String?                @db.VarChar
  c                    String?                @db.VarChar
  d                    String?                @db.VarChar
  type                 String?                @db.VarChar
  status               String?                @db.VarChar
  created_at           DateTime?              @db.Timestamp(6)
  updated_at           DateTime?              @db.Timestamp(6)
  inclass_level_id     Int?
  inclass_category_id  Int?
  inclass_levels       inclass_levels?        @relation(fields: [inclass_level_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_test_fk")
  inclass_category     inclass_category?      @relation(fields: [inclass_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_test_fk2")
  inclass_test_mapping inclass_test_mapping[]
}

model inclass_test_mapping {
  id                  Int               @id(map: "inclass_test_mapping_pk") @default(autoincrement())
  inclass_test_id     Int?
  inclass_training_id Int?
  type                String?           @db.VarChar
  created_at          DateTime?         @db.Timestamp(6)
  updated_at          DateTime?         @db.Timestamp(6)
  inclass_test        inclass_test?     @relation(fields: [inclass_test_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_test_mapping_fk")
  inclass_training    inclass_training? @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_test_mapping_fk_1")

  @@unique([inclass_test_id, inclass_training_id, type], map: "inclass_test_mapping_un")
}

model inclass_trainer {
  id                  Int               @id(map: "inclass_trainer_pk") @default(autoincrement())
  name                String?           @db.VarChar
  inclass_training_id Int?
  created_at          DateTime?         @db.Timestamp(6)
  updated_at          DateTime?         @db.Timestamp(6)
  type                String?           @db.VarChar
  job_name            String?           @db.VarChar
  npk                 String?           @db.VarChar
  inclass_training    inclass_training? @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_trainer_fk")

  @@unique([name, inclass_training_id], map: "inclass_trainer_un")
}

model inclass_training {
  id                                                               Int                         @id(map: "inclass_training_pk") @default(autoincrement())
  name                                                             String?                     @db.VarChar
  pic                                                              String?                     @db.VarChar
  group_user                                                       String?                     @db.VarChar
  has_pretest                                                      Boolean?
  has_posttest                                                     Boolean?
  created_at                                                       DateTime?                   @db.Timestamp(6)
  updated_at                                                       DateTime?                   @db.Timestamp(6)
  status                                                           String?                     @db.VarChar
  pretest_passing_grade                                            Int?
  posttest_passing_grade                                           Int?
  code                                                             String?                     @db.VarChar
  description                                                      String?
  is_deleted                                                       Boolean?
  updated_by                                                       String?                     @db.VarChar
  created_by                                                       String?                     @db.VarChar
  has_posttest_isian                                               Boolean?
  has_secondarytest                                                Boolean?
  posttest_pilihanganda_weightpercentage                           Int?
  final_score_passing_grade                                        Int?
  posttest_weightpercentage                                        Int?
  is_retake_posttest                                               Boolean?
  certification_background_id                                      Int?
  expired_after                                                    Int?
  pic_id                                                           Int?
  certification_pic_id                                             Int?
  events                                                           events[]
  inclass_certificate                                              inclass_certificate[]
  inclass_score                                                    inclass_score[]
  inclass_section_mapping                                          inclass_section_mapping[]
  inclass_test_mapping                                             inclass_test_mapping[]
  inclass_trainer                                                  inclass_trainer[]
  user_details_inclass_training_pic_idTouser_details               user_details?               @relation("inclass_training_pic_idTouser_details", fields: [pic_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_fk")
  certification_background                                         certification_background?   @relation(fields: [certification_background_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_fk_1")
  user_details_inclass_training_certification_pic_idTouser_details user_details?               @relation("inclass_training_certification_pic_idTouser_details", fields: [certification_pic_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_fk_2")
  inclass_training_progress                                        inclass_training_progress[]
  inclass_user_assignment                                          inclass_user_assignment[]
  inclass_user_test_answers                                        inclass_user_test_answers[]
  inclass_user_test_result                                         inclass_user_test_result[]
}

model inclass_training_attendance {
  id           Int           @id(map: "inclass_training_attendance_pk") @default(autoincrement())
  event_id     BigInt
  day          Int
  created_at   DateTime?     @db.Timestamp(6)
  updated_at   DateTime?     @db.Timestamp(6)
  npk          String        @db.VarChar
  user_id      Int?
  events       events        @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_attendance_events_fk")
  user_details user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_attendance_user_details_fk")
}

model inclass_training_progress {
  id                        Int                         @id(map: "inclass_training_progress_pk") @default(autoincrement())
  event_id                  BigInt?
  inclass_training_id       Int?
  inclass_section_id        Int?
  status                    Boolean?
  specific_status           String?                     @db.VarChar
  user_id                   Int?
  npk                       String?                     @db.VarChar(255)
  issued_date               DateTime?                   @db.Timestamp(6)
  completed_date            DateTime?                   @db.Timestamp(6)
  events                    events?                     @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_progress_events_fk")
  inclass_sections          inclass_sections?           @relation(fields: [inclass_section_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_progress_inclass_sections_fk")
  inclass_training          inclass_training?           @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_progress_inclass_training_fk")
  user_details              user_details?               @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_training_progress_user_details_fk")
  inclass_user_assignment   inclass_user_assignment[]
  inclass_user_test_answers inclass_user_test_answers[]
  inclass_user_test_result  inclass_user_test_result[]
}

model inclass_user_assignment {
  id                           Int                        @id(map: "inclass_user_assignment_pk") @default(autoincrement())
  inclass_section_id           Int?
  user_id                      Int?
  event_id                     BigInt?
  inclass_training_id          Int?
  link                         String?                    @db.VarChar
  document_name                String?                    @db.VarChar
  file_format                  String?                    @db.VarChar
  file_size                    String?                    @db.VarChar
  file_upload_time             DateTime?                  @db.Timestamp(6)
  inclass_training_progress_id Int?
  inclass_sections             inclass_sections?          @relation(fields: [inclass_section_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_assignment_fk")
  inclass_training             inclass_training?          @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_assignment_fk_1")
  events                       events?                    @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_assignment_fk_2")
  user_details                 user_details?              @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_assignment_fk_3")
  inclass_training_progress    inclass_training_progress? @relation(fields: [inclass_training_progress_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_assignment_fk_4")
}

model inclass_user_test_answers {
  id                           Int                        @id(map: "inclass_user_test_answers_pk") @default(autoincrement())
  npk                          String?                    @db.VarChar
  inclass_test_id              Int?
  answer                       String?
  event_id                     BigInt?
  inclass_section_id           Int?
  user_id                      Int?
  attempt                      Int?
  score                        Int?
  inclass_training_id          Int?
  test_question_id             Int?
  inclass_training_progress_id Int?
  events                       events?                    @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_answers_events_fk")
  inclass_sections             inclass_sections?          @relation(fields: [inclass_section_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_answers_inclass_sections_fk")
  inclass_training             inclass_training?          @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_answers_inclass_training_fk")
  inclass_training_progress    inclass_training_progress? @relation(fields: [inclass_training_progress_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_answers_inclass_training_progress_fk")
  question                     question?                  @relation(fields: [test_question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_answers_question_fk")
  user_details                 user_details?              @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_answers_user_details_fk")
}

model inclass_user_test_result {
  id                           Int                        @id(map: "inclass_user_test_result_pk") @default(autoincrement())
  npk                          String?                    @db.VarChar
  score                        Int?
  status                       String?                    @db.VarChar
  type                         String?                    @db.VarChar
  inclass_training_id          Int?
  name                         String?                    @db.VarChar
  created_at                   DateTime?                  @db.Timestamp(6)
  updated_at                   DateTime?                  @db.Timestamp(6)
  event_id                     BigInt?
  result                       String?                    @db.VarChar
  inclass_section_id           Int?
  attempt                      Int?
  graded_date                  DateTime?                  @db.Timestamp(6)
  graded_by                    String?                    @db.VarChar
  passing_grade                Int?
  user_id                      Int?
  inclass_training_progress_id Int?
  events                       events?                    @relation(fields: [event_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_result_events_fk")
  inclass_training             inclass_training?          @relation(fields: [inclass_training_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_result_fk")
  inclass_sections             inclass_sections?          @relation(fields: [inclass_section_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_result_inclass_sections_fk")
  inclass_training_progress    inclass_training_progress? @relation(fields: [inclass_training_progress_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_result_inclass_training_progress_fk")
  user_details                 user_details?              @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "inclass_user_test_result_user_details_fk")
}

model job_functions {
  ID        Int         @id(map: "job_functions_pk") @default(autoincrement())
  name      String?     @db.VarChar(255)
  icon      String?     @db.VarChar
  avatar    String?     @db.VarChar
  job_names job_names[]
  jobs      jobs[]
  thread    thread[]
}

model job_modul {
  ID       Int     @id(map: "job_modul_pk") @default(autoincrement())
  job_id   Int?
  modul_id Int?
  jobs     jobs?   @relation(fields: [job_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_job_modul_TO_jobs_jobo3sqX")
  moduls   moduls? @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_job_modul_TO_moduls_mIRcfN")
}

model job_names {
  ID                      Int                @id(map: "job_names_pk") @default(autoincrement())
  job_name                String?            @db.VarChar
  level                   Int?
  group_user              String?            @db.VarChar
  department_name         String?            @db.VarChar
  level_name              String?            @db.VarChar
  is_manager              Boolean?
  job_function            String?            @db.VarChar
  job_id                  String             @unique(map: "job_names_un") @db.VarChar
  ho_dept                 String?            @db.VarChar
  entity_id               Int?
  job_position_type       String?            @db.VarChar
  is_need_neop            Boolean?
  is_need_welcoming_kit   Boolean?
  starter_module_priority String?            @db.VarChar
  job_function_id         Int?
  level_id                Int?
  is_active               Boolean?
  department_id           String?            @db.VarChar
  last_updated            DateTime?          @db.Timestamp(6)
  updated_by              String?            @db.VarChar
  created_at              DateTime?          @db.Timestamp(6)
  created_by              String?            @db.VarChar
  is_deleted              Boolean?
  alc_matrix              alc_matrix[]
  alc_result              alc_result[]
  events_jobname          events_jobname[]
  entity                  entity?            @relation(fields: [entity_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "job_names_fk")
  job_functions           job_functions?     @relation(fields: [job_function_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "job_names_fk_1")
  departments             departments?       @relation(fields: [department_id], references: [department_id], onDelete: NoAction, onUpdate: NoAction, map: "job_names_fk_2")
  levels                  levels?            @relation(fields: [level_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "job_names_fk_3")
  job_path_mapping        job_path_mapping[]
  learning_job            learning_job[]
  user_details            user_details[]

  @@index([ID, level], map: "job_names_1_idx")
}

model job_path {
  ID                                 Int                                  @id(map: "job_path_pk") @default(autoincrement())
  name                               String?                              @unique(map: "job_path_un") @db.VarChar
  status                             String?                              @db.VarChar
  created_date                       DateTime?                            @db.Timestamp(6)
  updated_date                       DateTime?                            @db.Timestamp(6)
  branch_grade                       String?                              @db.VarChar
  level                              Int?
  type                               String?                              @db.VarChar
  career_path                        career_path[]
  career_plan                        career_plan[]
  job_path_mapping                   job_path_mapping[]
  tugas_competencies_aspirasi_detail tugas_competencies_aspirasi_detail[]
}

model job_path_mapping {
  ID           Int        @id(map: "job_path_mapping_pk") @default(autoincrement())
  job_path_id  Int?
  job_name_id  Int?
  status       String?    @db.VarChar
  created_date DateTime?  @db.Timestamp(6)
  updated_date DateTime?  @db.Timestamp(6)
  job_path     job_path?  @relation(fields: [job_path_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "job_path_mapping_fk")
  job_names    job_names? @relation(fields: [job_name_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "job_path_mapping_fk_1")

  @@unique([job_path_id, job_name_id], map: "job_path_mapping_un")
}

model jobs {
  ID                Int                 @id(map: "jobs_pk") @default(autoincrement())
  job_function_id   Int?
  name              String?             @db.VarChar(255)
  level             Int?
  detail            String?             @db.VarChar
  job_modul         job_modul[]
  job_functions     job_functions?      @relation(fields: [job_function_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_jobs_TO_job_functionsrbuCj")
  questionnaire_job questionnaire_job[]
}

model learning_job {
  ID               Int            @id(map: "learning_job_pk") @default(autoincrement())
  job_name_id      Int?
  learning_path_id Int?
  status           String?        @db.VarChar
  learning_path    learning_path? @relation(fields: [learning_path_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "learning_job_fk")
  job_names        job_names?     @relation(fields: [job_name_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "learning_job_fk_1")

  @@index([job_name_id, learning_path_id], map: "learning_job_job_name_id_idx")
}

model learning_modules {
  ID               Int            @id(map: "learning_modules_pk") @default(autoincrement())
  modul_id         Int?
  learning_path_id Int?
  moduls           moduls?        @relation(fields: [modul_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "learning_modules_fk")
  learning_path    learning_path? @relation(fields: [learning_path_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "learning_modules_fk_1")

  @@index([modul_id, learning_path_id], map: "learning_modules_modul_id_idx")
}

model learning_path {
  ID                 Int                  @id(map: "learning_path_pk") @default(autoincrement())
  code               String               @db.VarChar
  name               String?              @db.VarChar
  status             String?              @db.VarChar
  is_deleted         Boolean?
  last_updated       DateTime?            @db.Timestamp(6)
  updated_by         String?              @db.VarChar
  created_at         DateTime?            @db.Timestamp(6)
  created_by         String?              @db.VarChar
  learning_job       learning_job[]
  learning_modules   learning_modules[]
  learning_path_item learning_path_item[]
}

model learning_path_item {
  ID               Int            @id(map: "learning_path_item_pk") @default(autoincrement())
  level            Int?
  learning_path_id Int?
  status           String?        @db.VarChar
  learning_path    learning_path? @relation(fields: [learning_path_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "learning_path_item_fk")
}

model levels {
  ID                              Int                               @id(map: "levels_pk") @default(autoincrement())
  name                            String?                           @db.VarChar(255)
  level                           Int?
  status                          String?                           @db.VarChar
  acronym                         String?                           @db.VarChar
  is_deleted                      Boolean?
  last_updated                    DateTime?                         @db.Timestamp(6)
  updated_by                      String?                           @db.VarChar
  created_at                      DateTime?                         @db.Timestamp(6)
  created_by                      String?                           @db.VarChar
  image_level_mapping             image_level_mapping[]
  job_names                       job_names[]
  material_level_mapping          material_level_mapping[]
  moduls                          moduls[]
  question_level_mapping          question_level_mapping[]
  question_template_level_mapping question_template_level_mapping[]
  sub_section_level_mapping       sub_section_level_mapping[]

  @@index([ID, level, status], map: "levels_1_idx")
}

model likes {
  ID        Int     @id(map: "likes_pk")
  npk       String? @db.VarChar
  thread_id Int?
  state     Int?
  thread    thread? @relation(fields: [thread_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "likes_fk")
}

model log_insert_detail {
  id         Int         @id(map: "log_insert_detail_pk") @default(autoincrement())
  id_upload  Int?
  row        String?     @db.VarChar
  status     String?     @db.VarChar
  reason     String?     @db.VarChar
  upload_dev upload_dev? @relation(fields: [id_upload], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "log_insert_detail_fk")
}

model material_category_mapping {
  id                      Int                   @id(map: "material_category_mapping_pk") @default(autoincrement())
  materials_repository_id Int?
  category_id             Int?
  materials_repository    materials_repository? @relation(fields: [materials_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "material_category_mapping_fk")
  category                category?             @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "material_category_mapping_fk_1")
}

model material_level_mapping {
  id                      Int                   @id(map: "material_level_mapping_pk") @default(autoincrement())
  materials_repository_id Int?
  levels_id               Int?
  materials_repository    materials_repository? @relation(fields: [materials_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "material_level_mapping_fk")
  levels                  levels?               @relation(fields: [levels_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "material_level_mapping_fk_1")
}

model materials_repository {
  id                        Int                         @id(map: "materials_repository_pk") @default(autoincrement())
  type                      String?                     @db.VarChar
  name                      String?                     @db.VarChar
  file_format               String?                     @db.VarChar
  link                      String?                     @db.VarChar
  filesize                  Int?
  is_deleted                Boolean?
  created_at                DateTime?                   @db.Timestamp(6)
  created_by                String?                     @db.VarChar
  last_updated              DateTime?                   @db.Timestamp(6)
  updated_by                String?                     @db.VarChar
  feature                   String?                     @db.VarChar
  inclass_sections          inclass_sections[]
  material_category_mapping material_category_mapping[]
  material_level_mapping    material_level_mapping[]
  section                   section[]
  sub_section               sub_section[]
}

model modul_assignment {
  ID                        Int                         @id(map: "modul_assignment_pk") @default(autoincrement())
  modul_id                  Int?
  npk                       String?                     @db.VarChar(255)
  status                    String?                     @db.VarChar(255)
  assigned_date             DateTime?                   @db.Timestamp(6)
  last_opened               DateTime?                   @db.Timestamp(6)
  user_id                   Int?
  is_retake_posttest        Boolean?
  attempt                   Int?
  completed_date            DateTime?                   @db.Timestamp(6)
  moduls                    moduls?                     @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_modul_assignment_TO_mHkIrX")
  user_details              user_details?               @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_assignment_fk")
  modul_assignment_progress modul_assignment_progress[]
  modul_certificate         modul_certificate[]

  @@index([modul_id, user_id, assigned_date], map: "modul_assignment_modul_id_idx")
}

model modul_assignment_progress {
  ID                   Int                @id(map: "sub_section_assignment_pkey") @default(autoincrement())
  modul_id             Int?
  sub_modul_id         Int?
  sub_section_id       Int?
  status               Boolean?
  npk                  String?            @db.VarChar(255)
  mandatory_section_id Int?
  specific_status      String?            @db.VarChar
  user_id              Int?
  last_page_number     String?            @db.VarChar
  last_time_player     String?            @db.VarChar
  module_assignment_id Int?
  order                Int?
  issued_date          DateTime?          @db.Timestamp(6)
  completed_date       DateTime?          @db.Timestamp(6)
  sub_section          sub_section?       @relation(fields: [sub_section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_modul_assignment_statVqnza")
  sub_moduls           sub_moduls?        @relation(fields: [sub_modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_modul_assignment_statupvLK")
  moduls               moduls?            @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_sub_section_assignmenfMaKD")
  user_details         user_details?      @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_assignment_progress_fk")
  modul_assignment     modul_assignment?  @relation(fields: [module_assignment_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_assignment_progress_fk_1")
  user_assignment      user_assignment[]
  user_test_answer     user_test_answer[]
  user_test_result     user_test_result[]
}

model modul_category {
  ID            Int      @id(map: "modul_category_pk") @default(autoincrement())
  category_name String?  @db.VarChar
  moduls        moduls[]
}

model modul_category_mapping {
  id          Int       @id(map: "modul_category_mapping_pk") @default(autoincrement())
  moduls_id   Int?
  category_id Int?
  moduls      moduls?   @relation(fields: [moduls_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_category_mapping_fk")
  category    category? @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "modul_category_mapping_fk_1")
}

model modul_certificate {
  id                  Int               @id(map: "modul_certificate_pk") @default(autoincrement())
  user_id             Int?
  module_id           Int?
  modul_assignment_id Int?
  issued_date         DateTime?         @db.Timestamp(6)
  expired_date        DateTime?         @db.Timestamp(6)
  status              String?           @db.VarChar
  link                String?           @db.VarChar
  user_details        user_details?     @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_certificate_fk")
  moduls              moduls?           @relation(fields: [module_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_certificate_fk_1")
  modul_assignment    modul_assignment? @relation(fields: [modul_assignment_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_certificate_fk_2")

  @@index([user_id, module_id, status], map: "modul_certificate_user_id_idx")
}

model modul_shared_files {
  id            Int           @id(map: "modul_shared_files_pk") @default(autoincrement())
  modul_id      Int?
  document_name String?       @db.VarChar
  file_format   String?       @db.VarChar
  file_size     Int?
  link          String?       @db.VarChar
  uploaded_type String?       @db.VarChar
  user_id       Int?
  is_deleted    Boolean?
  last_updated  DateTime?     @db.Timestamp(6)
  updated_by    String?       @db.VarChar
  uploaded_at   DateTime?     @db.Timestamp(6)
  uploaded_by   String?       @db.VarChar
  moduls        moduls?       @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_shared_files_fk")
  user_details  user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_shared_files_fk_1")
}

model modul_suggestions {
  ID        Int      @id(map: "modul_suggestions_pk") @default(autoincrement())
  npk       String?  @db.VarChar(255)
  modul_id  Int?
  mandatory Boolean?
  moduls    moduls?  @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "modul_suggestions_fk")
}

model modul_view_duration {
  id         BigInt    @id @default(autoincrement())
  modul_id   Int
  npk        String    @db.VarChar
  username   String?   @db.VarChar
  start_time DateTime? @db.Timestamp(6)
  end_time   DateTime? @db.Timestamp(6)
  moduls     moduls    @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction)
}

model modul_viewed_by {
  id         BigInt    @id(map: "modul_viewed_by_pk") @default(autoincrement())
  modul_id   Int?
  npk        String?   @db.VarChar
  created_at DateTime? @db.Timestamp(6)
  moduls     moduls?   @relation(fields: [modul_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "modul_viewed_by_fk")
}

model module_log_history {
  id                 Int       @id(map: "module_log_history_pk") @default(autoincrement())
  module_id          Int?
  content_type       String?   @db.VarChar
  content_name       String?   @db.VarChar
  related_content_id Int?
  original_content   String?   @db.VarChar
  updated_content    String?   @db.VarChar
  created_at         DateTime? @db.Timestamp(6)
  created_by         String?   @db.VarChar
  action_type        String?   @db.VarChar
  moduls             moduls?   @relation(fields: [module_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "module_log_history_fk")
}

model moduls {
  ID                            Int                             @id(map: "moduls_pk") @default(autoincrement())
  name                          String?                         @db.VarChar(255)
  description                   String?
  cover_image                   String?
  duration                      Int?
  code                          String?                         @db.VarChar
  status                        String?
  requirement                   String?                         @db.VarChar
  type                          String?                         @db.VarChar
  made_for                      String?                         @db.VarChar
  categoryid                    Int?
  last_updated                  DateTime?                       @db.Timestamp(6)
  course_name                   String?                         @db.VarChar
  level_id                      Int?
  created_at                    DateTime?                       @db.Timestamp(6)
  modul_icon                    String?
  is_deleted                    Boolean?
  updated_by                    String?                         @db.VarChar
  created_by                    String?                         @db.VarChar
  modul_cover                   String?                         @db.VarChar
  entity_id                     Int?
  certification_background_id   Int?
  certification_pic_id          Int?
  final_score_passing_grade     Int?
  posttest_weightpercentage     Int?
  expired_after                 Int?
  is_recommendation             Boolean?
  job_modul                     job_modul[]
  learning_modules              learning_modules[]
  modul_assignment              modul_assignment[]
  modul_assignment_progress     modul_assignment_progress[]
  modul_category_mapping        modul_category_mapping[]
  modul_certificate             modul_certificate[]
  modul_shared_files            modul_shared_files[]
  modul_suggestions             modul_suggestions[]
  modul_view_duration           modul_view_duration[]
  modul_viewed_by               modul_viewed_by[]
  module_log_history            module_log_history[]
  modul_category                modul_category?                 @relation(fields: [categoryid], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "moduls_fk")
  entity                        entity?                         @relation(fields: [entity_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "moduls_fk_2")
  user_details                  user_details?                   @relation(fields: [certification_pic_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "moduls_fk_3")
  certification_background      certification_background?       @relation(fields: [certification_background_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "moduls_fk_4")
  levels                        levels?                         @relation(fields: [level_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "moduls_levels_fk")
  moduls_entity_mapping         moduls_entity_mapping[]
  related_skills_module_mapping related_skills_module_mapping[]
  reviews                       reviews[]
  skill_modul                   skill_modul[]
  sub_moduls                    sub_moduls[]
  tag_modul                     tag_modul[]
  tugas_modul                   tugas_modul[]
  tugas_progress                tugas_progress[]
  user_assignment               user_assignment[]
  user_certification            user_certification[]
  user_test_answer              user_test_answer[]

  @@index([ID, status, type, is_deleted, entity_id], map: "moduls_2_idx")
  @@index([ID, name, status, type], map: "moduls_id_idx")
}

model moduls_entity_mapping {
  id        Int     @id(map: "moduls_entity_mapping_pk") @default(autoincrement())
  module_id Int?
  entity_id Int?
  moduls    moduls? @relation(fields: [module_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "moduls_entity_mapping_fk")
  entity    entity? @relation(fields: [entity_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "moduls_entity_mapping_fk_1")

  @@index([module_id, entity_id], map: "moduls_entity_mapping_module_id_idx")
}

model notification {
  ID          Int       @id(map: "notification_pk") @default(autoincrement())
  npk         String    @db.VarChar
  message     String    @db.VarChar
  status      String?   @db.VarChar
  link        String?   @db.VarChar
  receivetime DateTime? @db.Timestamptz(6)
  title       String    @db.VarChar
  type        String    @db.VarChar
  highlights  String?   @db.VarChar
}

model otp {
  id           Int           @id(map: "otp_pk") @default(autoincrement())
  user_id      Int?
  otp          String?       @db.VarChar
  valid_until  DateTime?     @db.Timestamp(6)
  user_details user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "otp_fk")
}

model question {
  id                        Int                         @id(map: "question_pk") @default(autoincrement())
  question                  String?
  type                      String?                     @db.VarChar
  correct_answer            String?                     @db.VarChar
  a                         String?                     @db.VarChar
  b                         String?                     @db.VarChar
  c                         String?                     @db.VarChar
  d                         String?                     @db.VarChar
  image_id                  Int?
  is_deleted                Boolean?
  last_updated              DateTime?                   @db.Timestamp(6)
  updated_by                String?                     @db.VarChar
  created_at                DateTime?                   @db.Timestamp(6)
  created_by                String?                     @db.VarChar
  correct_answer_percentage BigInt?
  feature                   String?                     @db.VarChar
  inclass_user_test_answers inclass_user_test_answers[]
  image_repository          image_repository?           @relation(fields: [image_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_fk")
  question_category_mapping question_category_mapping[]
  question_level_mapping    question_level_mapping[]
  question_template_mapping question_template_mapping[]
  test_question_mapping     test_question_mapping[]
  user_test_answer          user_test_answer[]
}

model question_category_mapping {
  id          Int       @id(map: "question_category_mapping_pk") @default(autoincrement())
  question_id Int?
  category_id Int?
  question    question? @relation(fields: [question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_category_mapping_fk")
  category    category? @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_category_mapping_fk_1")
}

model question_level_mapping {
  id          Int       @id(map: "question_level_mapping_pk") @default(autoincrement())
  question_id Int?
  levels_id   Int?
  question    question? @relation(fields: [question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_level_mapping_fk")
  levels      levels?   @relation(fields: [levels_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "question_level_mapping_fk_1")
}

model question_template {
  id                                 Int                                  @id(map: "question_template_pk") @default(autoincrement())
  question_template_name             String?                              @db.VarChar
  type                               String?                              @db.VarChar
  is_deleted                         Boolean?
  last_updated                       DateTime?                            @db.Timestamp(6)
  updated_by                         String?                              @db.VarChar
  created_at                         DateTime?                            @db.Timestamp(6)
  created_by                         String?                              @db.VarChar
  feature                            String?                              @db.VarChar
  question_template_category_mapping question_template_category_mapping[]
  question_template_level_mapping    question_template_level_mapping[]
  question_template_mapping          question_template_mapping[]
}

model question_template_category_mapping {
  id                   Int                @id(map: "question_template_category_mapping_pk") @default(autoincrement())
  question_template_id Int?
  category_id          Int?
  question_template    question_template? @relation(fields: [question_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_template_category_mapping_fk")
  category             category?          @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_template_category_mapping_fk_1")
}

model question_template_level_mapping {
  id                   Int                @id(map: "question_template_level_mapping_pk") @default(autoincrement())
  question_template_id Int?
  levels_id            Int?
  question_template    question_template? @relation(fields: [question_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_template_level_mapping_fk")
  levels               levels?            @relation(fields: [levels_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "question_template_level_mapping_fk_1")
}

model question_template_mapping {
  id                   Int                @id(map: "question_template_mapping_pk") @default(autoincrement())
  question_id          Int?
  question_template_id Int?
  question_template    question_template? @relation(fields: [question_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_template_mapping_fk")
  question             question?          @relation(fields: [question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "question_template_mapping_fk_1")
}

model questionnaire {
  ID                 Int     @id(map: "questionnaire_pk") @default(autoincrement())
  npk                String? @db.VarChar
  answerjobposition1 String? @db.VarChar
  answerjobposition2 String? @db.VarChar
  answerjobposition3 String? @db.VarChar
  answermodul1       String? @db.VarChar
  answermodul2       String? @db.VarChar
  answermodul3       String? @db.VarChar
}

model questionnaire_job {
  ID     Int  @id(map: "questionnaire_job_pk") @default(autoincrement())
  job_id Int
  jobs   jobs @relation(fields: [job_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "questionnaire_job_fk")
}

model quiz {
  ID             Int          @id(map: "quiz_pk") @default(autoincrement())
  sub_modul_id   Int?
  sub_section_id Int?
  question       String?      @db.VarChar
  type           String?      @db.VarChar
  correct_answer String?      @db.VarChar
  a              String?      @db.VarChar
  b              String?      @db.VarChar
  c              String?      @db.VarChar
  d              String?      @db.VarChar
  created_date   DateTime?    @db.Timestamp(6)
  updated_date   DateTime?    @db.Timestamp(6)
  created_by     String?      @db.VarChar
  updated_by     String?      @db.VarChar
  status         Boolean?
  sub_moduls     sub_moduls?  @relation(fields: [sub_modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "quiz_fk")
  sub_section    sub_section? @relation(fields: [sub_section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "quiz_fk_1")
}

model recent_books {
  id          Int       @id(map: "recent_books_pk") @default(autoincrement())
  npk         String?   @db.VarChar
  cover       String?   @db.VarChar
  title       String?   @db.VarChar
  authors     String?   @db.VarChar
  slug        String?   @db.VarChar
  created_at  DateTime? @db.Timestamptz(6)
  last_opened DateTime? @db.Timestamptz(6)

  @@unique([npk, slug], map: "recent_books_un")
}

model related_skills {
  id                            Int                             @id(map: "related_skills_pk") @default(autoincrement())
  related_skills_name           String?                         @db.VarChar
  certificate_skills_name       String?                         @db.VarChar
  related_skills_module_mapping related_skills_module_mapping[]
}

model related_skills_module_mapping {
  id               Int             @id(map: "related_skills_module_mapping_pk") @default(autoincrement())
  related_skill_id Int?
  modul_id         Int?
  related_skills   related_skills? @relation(fields: [related_skill_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "related_skills_module_mapping_fk")
  moduls           moduls?         @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "related_skills_module_mapping_fk_1")
}

model reviews {
  ID              Int           @id @default(autoincrement())
  modul_id        Int?
  npk             String?       @db.VarChar(255)
  comments        String?
  ratings         Int?
  name            String?       @db.VarChar(255)
  profile_picture String?       @db.VarChar(255)
  user_id         Int?
  created_at      DateTime?     @db.Timestamp(6)
  moduls          moduls?       @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_reviews_TO_moduls_modGYuwQ")
  user_details    user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "reviews_fk")

  @@index([ID, modul_id, user_id, ratings], map: "reviews_1_idx")
}

model roles {
  id             Int            @id(map: "roles_pk") @default(autoincrement())
  user_role_name String?        @db.VarChar
  is_deleted     Boolean?
  last_updated   DateTime?      @db.Timestamp(6)
  updated_by     String?        @db.VarChar
  created_at     DateTime?      @db.Timestamp(6)
  created_by     String?        @db.VarChar
  user_details   user_details[]
  user_roles     user_roles[]
}

model section {
  id                      Int                   @id(map: "section_pk") @default(autoincrement())
  section_name            String?               @db.VarChar
  type                    String?               @db.VarChar
  step                    Int?
  materials_repository_id Int?
  passing_grade           Int?
  number_of_question      Int?
  duration                Int?
  is_deleted              Boolean?
  last_updated            DateTime?             @db.Timestamp(6)
  updated_by              String?               @db.VarChar
  created_at              DateTime?             @db.Timestamp(6)
  created_by              String?               @db.VarChar
  materials_repository    materials_repository? @relation(fields: [materials_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "section_fk")
}

model section_log_progres {
  id           Int       @id(map: "section_log_progres_pk") @default(autoincrement())
  user_npk     String?   @db.VarChar
  modul_id     Int?
  submodul_id  Int?
  section_id   Int?
  type         String?   @db.VarChar
  description  String?   @db.VarChar
  created_date DateTime? @db.Timestamp(6)
  updated_date DateTime? @db.Timestamp(6)
}

model skill_modul {
  ID       Int     @id @default(autoincrement())
  skill_id Int?
  modul_id Int?
  moduls   moduls? @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_skill_modul_TO_modulsSvvmp")
  skills   skills? @relation(fields: [skill_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_skill_modul_TO_skillsOSQyG")
}

model skills {
  ID          Int           @id @default(autoincrement())
  name        String?       @db.VarChar(255)
  skill_modul skill_modul[]
}

model sliders {
  ID            Int       @id(map: "sliders_pk") @default(autoincrement())
  slider        String?   @db.VarChar
  link          String?   @db.VarChar
  slider_name   String?   @db.VarChar
  slider_mobile String?   @db.VarChar
  is_deleted    Boolean?  @default(false) @db.Boolean
  created_by    String?   @db.VarChar
  created_at    DateTime? @db.Timestamp(6)
  updated_by    String?   @db.VarChar
  updated_at    DateTime? @db.Timestamp(6)
}

model sub_category {
  id                Int                @id(map: "sub_category_pk") @default(autoincrement())
  sub_category_name String?            @db.VarChar
  category_id       Int?
  is_deleted        Boolean?
  last_updated      DateTime?          @db.Timestamp(6)
  updated_by        String?            @db.VarChar
  created_at        DateTime?          @db.Timestamp(6)
  created_by        String?            @db.VarChar
  inclass_sections  inclass_sections[]
  category          category?          @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sub_category_fk")
}

model sub_moduls {
  ID                                     Int                          @id(map: "lectures_pk") @default(autoincrement())
  modul_id                               Int?
  name                                   String?                      @db.VarChar(255)
  section                                Int?
  type                                   String?                      @db.VarChar
  passing_grade                          Int?
  created_date                           DateTime?                    @db.Timestamp(6)
  updated_date                           DateTime?                    @db.Timestamp(6)
  is_deleted                             Boolean?
  updated_by                             String?                      @db.VarChar
  created_by                             String?                      @db.VarChar
  posttest_pilihanganda_weightpercentage Int?
  is_retake_posttest                     Boolean?
  status                                 String?                      @db.VarChar
  modul_assignment_progress              modul_assignment_progress[]
  quiz                                   quiz[]
  moduls                                 moduls?                      @relation(fields: [modul_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "sub_moduls_fk")
  sub_moduls_section_mapping             sub_moduls_section_mapping[]
  sub_section                            sub_section[]
  test                                   test[]
  user_assignment                        user_assignment[]
  user_test_answer                       user_test_answer[]
  user_test_result                       user_test_result[]
}

model sub_moduls_section_mapping {
  id            Int          @id(map: "sub_moduls_section_mapping_pk") @default(autoincrement())
  sub_moduls_id Int?
  section_id    Int?
  sub_moduls    sub_moduls?  @relation(fields: [sub_moduls_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "sub_moduls_section_mapping_fk")
  sub_section   sub_section? @relation(fields: [section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "sub_moduls_section_mapping_fk_1")
}

model sub_section {
  ID                           Int                            @id @default(autoincrement())
  sub_modul_id                 Int?
  type                         String?                        @db.VarChar(255)
  step                         Int?
  link                         String?                        @db.VarChar
  name                         String?                        @db.VarChar(255)
  duration                     Int?
  materials_repository_id      Int?
  passing_grade                Int?
  number_of_question           Int?
  last_updated                 DateTime?                      @db.Timestamp(6)
  updated_by                   String?                        @db.VarChar
  created_at                   DateTime?                      @db.Timestamp(6)
  created_by                   String?                        @db.VarChar
  is_deleted                   Boolean?
  assignment_instruction       String?                        @db.VarChar
  with_test_timer              Boolean?
  competencies_section         competencies_section[]
  modul_assignment_progress    modul_assignment_progress[]
  quiz                         quiz[]
  sub_moduls_section_mapping   sub_moduls_section_mapping[]
  sub_moduls                   sub_moduls?                    @relation(fields: [sub_modul_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "sub_section_fk")
  materials_repository         materials_repository?          @relation(fields: [materials_repository_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sub_section_fk1")
  sub_section_category_mapping sub_section_category_mapping[]
  sub_section_level_mapping    sub_section_level_mapping[]
  test                         test[]
  test_question_mapping        test_question_mapping[]
  tugas_progress               tugas_progress[]
  tugas_section                tugas_section[]
  user_assignment              user_assignment[]
  user_test_answer             user_test_answer[]
}

model sub_section_category_mapping {
  id             Int          @id(map: "sub_section_category_mapping_pk") @default(autoincrement())
  sub_section_id Int?
  category_id    Int?
  category       category?    @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sub_section_category_mapping_fk")
  sub_section    sub_section? @relation(fields: [sub_section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "sub_section_category_mapping_fk_1")
}

model sub_section_level_mapping {
  id             Int          @id(map: "sub_section_level_mapping_pk") @default(autoincrement())
  sub_section_id Int?
  levels_id      Int?
  sub_section    sub_section? @relation(fields: [sub_section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "sub_section_level_mapping_fk")
  levels         levels?      @relation(fields: [levels_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "sub_section_level_mapping_fk_1")
}

model survey {
  id          Int       @id(map: "survey_pk") @default(autoincrement())
  title       String    @db.VarChar
  name        String    @db.VarChar
  description String?   @db.VarChar
  url         String?   @db.VarChar
  created_at  DateTime? @db.Timestamptz(6)
  sm_id       Int
  status      String    @default("active") @db.VarChar
  updated_at  DateTime? @db.Timestamptz(6)
}

model tag_modul {
  ID       Int     @id @default(autoincrement())
  modul_id Int?
  tag_id   Int?
  tags     tags?   @relation(fields: [tag_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_tag_modul_TO_tags_tagO5McM")
  moduls   moduls? @relation(fields: [modul_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "tag_modul_fk")
}

model tags {
  ID        Int         @id @default(autoincrement())
  name      String?     @db.VarChar(255)
  faq_tags  faq_tags[]
  tag_modul tag_modul[]
}

model team_assignment {
  npk        String    @db.VarChar(10)
  dmnpk      String?   @db.VarChar(10)
  start_date DateTime  @db.Date
  end_date   DateTime? @db.Date

  @@id([npk, start_date], map: "team_assignment_pk")
  @@index([start_date, end_date], map: "team_assignment_idx1")
}

model tecat_external_task {
  id                     Int                 @id(map: "tecat_external_task_pk") @default(autoincrement())
  name                   String?             @db.VarChar
  url                    String?             @db.VarChar
  tecat_matrix_detail_id Int
  tecat_matrix_detail    tecat_matrix_detail @relation(fields: [tecat_matrix_detail_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "tecat_external_task_fk")
}

model tecat_matrix {
  id              String    @id(map: "tecat_matrix_pk") @db.VarChar
  job_id          String?   @db.VarChar
  job_name        String?   @db.VarChar
  competency_id   Int?
  competency_code String?   @db.VarChar
  competency_name String?   @db.VarChar
  level           Int?
  flag_active     Boolean?
  created_at      DateTime? @db.Timestamp(6)
  updated_at      DateTime? @db.Timestamp(6)
}

model tecat_matrix_detail {
  id                    Int                   @id(map: "tecat_matrix_detail_pk") @default(autoincrement())
  competency_id         Int?
  level                 Int
  requirement_per_level Float?                @db.Real
  requirement_all_level Float?                @db.Real
  description           String?
  assignment_status     Boolean?
  tecat_external_task   tecat_external_task[]
  competencies          competencies?         @relation(fields: [competency_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "tecat_matrix_detail_fk")
}

model tecat_result {
  id               BigInt    @id(map: "tecat_report_pk") @default(autoincrement())
  npk              String?   @db.VarChar
  job_name         String?   @db.VarChar
  competency_code  String?   @db.VarChar
  competency_name  String?   @db.VarChar
  total_question   Int?
  correct          Int?
  incorrect        Int?
  percentage       Float?    @db.Real
  competency_level Int?
  created_at       DateTime? @db.Timestamp(0)
}

model tecat_result_backup {
  id               BigInt    @id(map: "tecat_report_backup_pk") @default(autoincrement())
  npk              String?   @db.VarChar
  job_name         String?   @db.VarChar
  competency_code  String?   @db.VarChar
  competency_name  String?   @db.VarChar
  total_question   Int?
  correct          Int?
  incorrect        Int?
  percentage       Float?    @db.Real
  competency_level Int?
  created_at       DateTime? @db.Timestamp(0)
}

model test {
  ID             Int          @id(map: "test_pk") @default(autoincrement())
  sub_modul_id   Int?
  question       String?
  type           String?      @db.VarChar
  sub_section_id Int?
  category       String?      @db.VarChar
  correct_answer String?      @db.VarChar
  a              String?      @db.VarChar
  b              String?      @db.VarChar
  c              String?      @db.VarChar
  d              String?      @db.VarChar
  created_date   DateTime?    @db.Timestamp(6)
  updated_date   DateTime?    @db.Timestamp(6)
  created_by     String?      @db.VarChar
  updated_by     String?      @db.VarChar
  status         Boolean?
  number         String?      @db.VarChar(1024)
  sub_moduls     sub_moduls?  @relation(fields: [sub_modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_test_TO_sub_moduls_suWMXl5")
  sub_section    sub_section? @relation(fields: [sub_section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "test_fk")
}

model test_question_mapping {
  id                 Int               @id(map: "test_question_mapping_pk") @default(autoincrement())
  section_id         Int?
  question_id        Int?
  inclass_section_id Int?
  sub_section        sub_section?      @relation(fields: [section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "test_question_mapping_fk")
  question           question?         @relation(fields: [question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "test_question_mapping_fk_1")
  inclass_sections   inclass_sections? @relation(fields: [inclass_section_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "test_question_mapping_fk_2")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model test_timezone {
  created_at DateTime? @db.Timestamptz(6)
  label      String?   @db.VarChar

  @@ignore
}

model thread {
  ID              Int            @id(map: "thread_pk") @default(autoincrement())
  job_function_id Int?
  title           String?        @db.VarChar
  content         String?
  created_date    String         @db.VarChar
  likes           Int?           @default(0)
  dislikes        Int?           @default(0)
  creator         String?        @db.VarChar
  reported        Int?
  comments        comments[]
  likes_relation  likes[]
  job_functions   job_functions? @relation(fields: [job_function_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "thread_fk")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model tmp_npk_nonactive {
  npk String? @db.VarChar(5)

  @@ignore
}

model tugas {
  id                                 Int                                  @id(map: "tugas_pk") @default(autoincrement())
  instruksi                          String?
  due_date                           DateTime?                            @db.Date
  created_at                         DateTime?                            @db.Timestamp(6)
  created_by                         String?                              @db.VarChar
  created_by_name                    String?                              @db.VarChar
  created_by_jobname                 String?                              @db.VarChar
  is_idp                             Boolean                              @default(false)
  type                               String?                              @db.VarChar
  user_id                            Int?
  user_details                       user_details?                        @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "tugas_fk")
  tugas_competencies_aspirasi_detail tugas_competencies_aspirasi_detail[]
  tugas_modul                        tugas_modul[]
  tugas_other_resources              tugas_other_resources[]
  tugas_progress                     tugas_progress[]
  tugas_section                      tugas_section[]
  tugas_users                        tugas_users[]
}

model tugas_competencies_aspirasi_detail {
  id              Int          @id(map: "tugas_competencies_aspirasi_detail_pk") @default(autoincrement())
  tugas_id        Int?
  competency_id   Int
  tecat_level     Int?
  alc_requirement Float?       @db.Real
  job_path_id     Int?
  tugas           tugas?       @relation(fields: [tugas_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "tugas_competencies_aspirasi_detail_fk")
  competencies    competencies @relation(fields: [competency_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "tugas_competencies_aspirasi_detail_fk_1")
  job_path        job_path?    @relation(fields: [job_path_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "tugas_competencies_aspirasi_detail_fk_2")
}

model tugas_modul {
  id       Int     @id(map: "tugas_modul_pk") @default(autoincrement())
  tugas_id Int?
  modul_id Int?
  tugas    tugas?  @relation(fields: [tugas_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "tugas_modul_fk")
  moduls   moduls? @relation(fields: [modul_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "tugas_modul_fk_1")
}

model tugas_other_resources {
  id             Int              @id(map: "tugas_other_resources_pk") @default(autoincrement())
  tugas_id       Int?
  name           String?          @db.VarChar
  url            String?
  tugas          tugas?           @relation(fields: [tugas_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "tugas_other_resources_fk")
  tugas_progress tugas_progress[]
}

model tugas_progress {
  id                       Int                    @id(map: "tugas_progress_pk") @default(autoincrement())
  tugas_id                 Int?
  section_id               Int?
  modul_id                 Int?
  tugas_other_resources_id Int?
  status                   Boolean?
  specific_status          String?                @db.VarChar
  user_id                  Int?
  npk                      String?                @db.VarChar(255)
  issued_date              DateTime?              @db.Timestamp(6)
  completed_date           DateTime?              @db.Timestamp(6)
  tugas                    tugas?                 @relation(fields: [tugas_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "tugas_progress_fk")
  sub_section              sub_section?           @relation(fields: [section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "tugas_progress_fk_1")
  moduls                   moduls?                @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "tugas_progress_fk_2")
  tugas_other_resources    tugas_other_resources? @relation(fields: [tugas_other_resources_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "tugas_progress_fk_3")
  user_details             user_details?          @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "tugas_progress_fk_4")
}

model tugas_section {
  id          Int          @id(map: "tugas_section_pk") @default(autoincrement())
  tugas_id    Int?
  section_id  Int?
  tugas       tugas?       @relation(fields: [tugas_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "tugas_section_fk")
  sub_section sub_section? @relation(fields: [section_id], references: [ID], onDelete: Cascade, onUpdate: NoAction, map: "tugas_section_fk_1")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tugas_users {
  id           Int           @id(map: "tugas_users_pk") @default(autoincrement())
  tugas_id     Int?
  npk          String?       @db.VarChar
  name         String?       @db.VarChar
  status       String?       @db.VarChar
  answer       String?
  review       String?
  submitted_at DateTime?     @db.Timestamp(6)
  reviewed_at  DateTime?     @db.Timestamp(6)
  jobname      String?       @db.VarChar
  user_id      Int?
  tugas        tugas?        @relation(fields: [tugas_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "tugas_users_fk")
  user_details user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "tugas_users_fk_1")
}

model upload_dev {
  id                Int                 @id(map: "upload_dev_pk") @default(autoincrement())
  filepath          String?             @db.VarChar
  flag              String?             @db.VarChar
  success           Int?
  failed            Int?
  filename          String?             @db.VarChar
  created_at        DateTime?           @db.Timestamp(6)
  updated_at        DateTime?           @db.Timestamp(6)
  created_by        String?             @db.VarChar
  updated_by        String?             @db.VarChar
  category          String?             @db.VarChar
  log_insert_detail log_insert_detail[]
}

model user_assignment {
  id                           Int                        @id(map: "user_assignment_pk") @default(autoincrement())
  section_id                   Int?
  sub_modul_id                 Int?
  modul_id                     Int?
  user_id                      Int?
  link                         String?                    @db.VarChar
  document_name                String?                    @db.VarChar
  file_format                  String?                    @db.VarChar
  file_size                    String?                    @db.VarChar
  modul_assignment_progress_id Int?
  file_upload_time             DateTime?                  @db.Timestamp(6)
  sub_section                  sub_section?               @relation(fields: [section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_assignment_fk")
  sub_moduls                   sub_moduls?                @relation(fields: [sub_modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_assignment_fk_1")
  moduls                       moduls?                    @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_assignment_fk_2")
  user_details                 user_details?              @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_assignment_fk_3")
  modul_assignment_progress    modul_assignment_progress? @relation(fields: [modul_assignment_progress_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_assignment_fk_4")
}

model user_certification {
  id               Int           @id(map: "user_certification_pk") @default(autoincrement())
  user_id          Int?
  applied_job_name String?       @db.VarChar
  applied_job_id   String?       @db.VarChar
  modul_id         Int?
  status           String?       @db.VarChar
  system_source    String?       @db.VarChar
  created_at       DateTime?     @db.Timestamp(6)
  created_by       String?       @db.VarChar
  last_updated     DateTime?     @db.Timestamp(6)
  updated_by       String?       @db.VarChar
  start_date       DateTime?     @db.Timestamp(6)
  end_date         DateTime?     @db.Timestamp(6)
  user_details     user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_certification_fk")
  moduls           moduls?       @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_certification_fk_1")
}

model user_details {
  npk                                                                  String?                       @db.VarChar
  second_email                                                         String?                       @db.VarChar
  ID                                                                   Int                           @id(map: "user_email_pk") @default(autoincrement())
  point                                                                Int?
  forum_title                                                          String?                       @default("Learner") @db.VarChar
  email                                                                String?                       @db.VarChar
  name                                                                 String?                       @db.VarChar
  job_name                                                             String?                       @db.VarChar
  created_at                                                           DateTime?                     @db.Timestamp(6)
  updated_at                                                           DateTime?                     @db.Timestamp(6)
  avatar                                                               String?                       @db.VarChar
  password                                                             String?                       @db.VarChar
  job_name_id                                                          Int?
  phone_number                                                         String?                       @db.VarChar
  user_type_id                                                         Int?
  user_role_id                                                         Int?
  supervisor_id                                                        Int?
  supervisor_name                                                      String?                       @db.VarChar
  supervisor_npk                                                       String?                       @db.VarChar
  is_active                                                            Boolean?
  is_need_neop                                                         Boolean?
  is_new_user                                                          Boolean?
  is_deleted                                                           Boolean?
  created_by                                                           String?                       @db.VarChar
  updated_by                                                           String?                       @db.VarChar
  forum_title_id                                                       Int?
  entity_id                                                            Int?
  is_need_welcoming_kit                                                Boolean?
  signature                                                            String?                       @db.VarChar
  accpedia_information                                                 accpedia_information[]
  career_plan                                                          career_plan[]
  events_npk                                                           events_npk[]
  events_participant                                                   events_participant[]
  inclass_certificate                                                  inclass_certificate[]
  inclass_score                                                        inclass_score[]
  inclass_section_mapping                                              inclass_section_mapping[]
  inclass_training_inclass_training_pic_idTouser_details               inclass_training[]            @relation("inclass_training_pic_idTouser_details")
  inclass_training_inclass_training_certification_pic_idTouser_details inclass_training[]            @relation("inclass_training_certification_pic_idTouser_details")
  inclass_training_attendance                                          inclass_training_attendance[]
  inclass_training_progress                                            inclass_training_progress[]
  inclass_user_assignment                                              inclass_user_assignment[]
  inclass_user_test_answers                                            inclass_user_test_answers[]
  inclass_user_test_result                                             inclass_user_test_result[]
  modul_assignment                                                     modul_assignment[]
  modul_assignment_progress                                            modul_assignment_progress[]
  modul_certificate                                                    modul_certificate[]
  modul_shared_files                                                   modul_shared_files[]
  moduls                                                               moduls[]
  otp                                                                  otp[]
  reviews                                                              reviews[]
  tugas                                                                tugas[]
  tugas_progress                                                       tugas_progress[]
  tugas_users                                                          tugas_users[]
  user_assignment                                                      user_assignment[]
  user_certification                                                   user_certification[]
  roles                                                                roles?                        @relation(fields: [user_role_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_details_fk")
  user_type                                                            user_type?                    @relation(fields: [user_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_details_fk1")
  entity                                                               entity?                       @relation(fields: [entity_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_details_fk2")
  forum_title_relation                                                 forum_title?                  @relation(fields: [forum_title_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_details_fk3")
  job_names                                                            job_names?                    @relation(fields: [job_name_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_details_fk4")
  user_roles                                                           user_roles[]
  user_test_answer                                                     user_test_answer[]
  user_test_result                                                     user_test_result[]

  @@index([ID, job_name_id, entity_id, is_need_welcoming_kit, is_need_neop], map: "user_details_id_idx")
}

model user_log_login {
  ID           Int       @id(map: "user_log_login_pk") @default(autoincrement())
  npk          String?   @db.VarChar(6)
  start_date   DateTime? @db.Timestamp(6)
  end_date     DateTime? @db.Timestamp(6)
  created_by   String?   @db.VarChar(50)
  created_date DateTime? @db.Date
  updated_by   String?   @db.VarChar(50)
  updated_date DateTime? @db.Date
  user_id      Int?      @unique(map: "user_log_login_un")
}

model user_roles {
  id           Int           @id(map: "user_roles_pk") @default(autoincrement())
  user_id      Int?
  role_id      Int?
  user_details user_details? @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_roles_fk")
  roles        roles?        @relation(fields: [role_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_roles_fk_1")
}

model user_test_answer {
  ID                           Int                        @id(map: "user_test_answer_pk") @default(autoincrement())
  sub_modul_id                 Int?
  modul_id                     Int?
  test_id                      Int?
  answer                       String?                    @db.VarChar(255)
  npk                          String?                    @db.VarChar
  test_question_id             Int?
  section_id                   Int?
  user_id                      Int?
  modul_assignment_progress_id Int?
  attempt                      Int?
  score                        Int?
  moduls                       moduls?                    @relation(fields: [modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_user_test_answer_TO_m76k7E")
  sub_moduls                   sub_moduls?                @relation(fields: [sub_modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_user_test_answer_TO_s2HnRg")
  question                     question?                  @relation(fields: [test_question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_test_answer_fk")
  sub_section                  sub_section?               @relation(fields: [section_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_test_answer_fk_2")
  user_details                 user_details?              @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_test_answer_fk_3")
  modul_assignment_progress    modul_assignment_progress? @relation(fields: [modul_assignment_progress_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_test_answer_fk_4")
}

model user_test_result {
  ID                           Int                        @id @default(autoincrement())
  npk                          String?                    @db.VarChar(255)
  sub_modul_id                 Int?
  score                        Int?
  status                       String?                    @db.VarChar
  type                         String?                    @db.VarChar
  sub_section_id               Int?
  created_date                 DateTime?                  @db.Timestamp(6)
  name                         String?                    @db.VarChar
  passing_grade                Int?
  user_id                      Int?
  modul_assignment_progress_id Int?
  attempt                      Int?
  graded_date                  DateTime?                  @db.Timestamp(6)
  graded_by                    String?                    @db.VarChar
  sub_moduls                   sub_moduls?                @relation(fields: [sub_modul_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "FK_user_test_result_TO_sz0YrT")
  user_details                 user_details?              @relation(fields: [user_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_test_result_fk")
  modul_assignment_progress    modul_assignment_progress? @relation(fields: [modul_assignment_progress_id], references: [ID], onDelete: NoAction, onUpdate: NoAction, map: "user_test_result_fk_1")
}

model user_type {
  id             Int            @id(map: "user_type_pk") @default(autoincrement())
  user_type_name String?        @db.VarChar
  is_deleted     Boolean?
  last_updated   DateTime?      @db.Timestamp(6)
  updated_by     String?        @db.VarChar
  created_at     DateTime?      @db.Timestamp(6)
  created_by     String?        @db.VarChar
  user_details   user_details[]
}
