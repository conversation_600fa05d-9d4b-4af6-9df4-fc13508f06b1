import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CDeleteMaterialBodyDto,
  CDeleteMaterialParamsDto,
} from '../dto/delete.dto';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { HttpStatus } from '@nestjs/common';

export const JDeleteMaterial = async (
  prisma: PrismaService,
  params: CDeleteMaterialParamsDto,
  body: CDeleteMaterialBodyDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.materials_repository
      .findUniqueOrThrow({
        where: {
          id: params.id,
        },
      })
      .catch(() => {
        throw handleError({
          message: 'material not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    await prisma.materials_repository.update({
      where: { id: params.id },
      data: {
        is_deleted: body.is_deleted,
        last_updated: new Date(),
        updated_by: null, // logged in username
      },
      select: { id: true },
    });

    return {
      status: true,
      message: 'success update delete data material',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
