import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { zBooleanOptional } from 'src/common/utils/validation.util';
import { z } from 'zod';

const getUserListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z
      .enum([
        'npk',
        'name',
        'job_name',
        'email',
        'second_email',
        'phone_number',
        'updated_by',
      ])
      .optional(),
    type_id: z.coerce.number().optional(),
    role_id: z.coerce.number().optional(),
    entity_id: z.coerce.number().optional(),
    is_neop: zBooleanOptional(),
    is_new_user: zBooleanOptional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetUserListQueryDto extends createZodDto(
  getUserListQuerySchema,
) {}

export interface IGetUserListResponseDto {
  id: number | null;
  npk: string | null;
  name: string | null;
  job_name: string | null;
  email: string | null;
  second_email: string | null;
  phone_number: string | null;
  user_type_id: number | null;
  user_type_name: string | null;
  user_role_id: number | null;
  user_role_name: string | null;
  entity_id: number | null;
  entity_name: string | null;
  is_need_neop: boolean | null;
  is_need_welcoming_kit: boolean | null;
  updated_at: Date | null;
  updated_by: string | null;
  is_active: boolean | null;
  is_new_user: boolean | null;
}
