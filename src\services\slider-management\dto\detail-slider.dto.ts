import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getSliderDetailParams = z.object({ id: z.coerce.number() });

export class CGetSliderDetailParamsDto extends createZodDto(
  getSliderDetailParams,
) {}

export interface IGetSliderDetailResponseDto {
  id: number;
  slider_name: string | null;
  slider_desktop: string | null;
  slider_mobile: string | null;
  link: string | null;
}
