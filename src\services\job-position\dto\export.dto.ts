import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const exportJobPositionQuerySchema = z.object({
  search: z.string().optional(),
  search_by: z.enum(['job_name', 'job_function']).optional(),
  job_position_type: z.string().optional(),
  level: z.coerce.number().optional(),
  is_neop: z
    .preprocess((val) => {
      if (typeof val === 'string') return val === 'true' || val === '1';
      if (typeof val === 'number') return val === 1;
      return val;
    }, z.boolean())
    .optional(),
  starter_modul_priority: z.string().optional(),
});

export class CExportJobPositionQueryDto extends createZodDto(
  exportJobPositionQuerySchema,
) {}
