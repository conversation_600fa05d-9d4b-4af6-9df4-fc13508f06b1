import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetImageRepositoryDetailParamsDto,
  IImageRepositoryDetailResponseDto,
} from '../dto/detail.dto';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetImageRepositoryDetail = async (
  prisma: PrismaService,
  params: CGetImageRepositoryDetailParamsDto,
): Promise<IGlobalResponseDto<IImageRepositoryDetailResponseDto>> => {
  try {
    const { id } = params;

    const data = await prisma.image_repository
      .findFirstOrThrow({
        where: {
          id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        include: {
          question: true,
          image_level_mapping: {
            include: {
              levels: true,
            },
          },
          image_category_mapping: {
            include: {
              category: true,
            },
          },
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
      })
      .catch(() => {
        throw handleError({
          message: 'image repository not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IImageRepositoryDetailResponseDto = {
      id: data.id,
      link: data.link,
      image_name: data.image_name,
      file_size: data.file_size,
      file_format: data.file_format,
      levels: data.image_level_mapping
        .filter((ml) => ml.levels)
        .map((ml) => ({
          id: ml.levels?.ID ?? null,
          name: ml.levels?.name ?? null,
        })),
      categories: data.image_category_mapping
        .filter((mc) => mc.category)
        .map((mc) => ({
          id: mc.category?.id ?? null,
          name: mc.category?.category_name ?? null,
        })),
      associated: data.question.map((q) => ({
        question_id: q.id ?? null,
        question: q.question ?? null,
      })),
      feature: data.feature,
      created_at: data.created_at ? data.created_at.toISOString() : null,
      created_by: data.created_by,
      updated_by: data.updated_by,
      last_updated: data.last_updated ? data.last_updated.toISOString() : null,
    };

    return {
      status: true,
      message: 'success get data image repository',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
