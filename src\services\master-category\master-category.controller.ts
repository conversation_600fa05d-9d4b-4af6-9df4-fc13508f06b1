import { Body, Controller, Get, Param, Post, Query, Res } from '@nestjs/common';
import { MasterCategoryService } from './master-category.service';
import { CCreateCategoryDto } from './dto/create.dto';
import { CGetCategoryDetailParamsDto } from './dto/detail.dto';
import { CExportCategoryQueryDto } from './dto/export.dto';
import * as express from 'express';
import dayjs from 'dayjs';
import { CGetCategoryListQueryDto } from './dto/list.dto';
import { CUpdateCategoryDto, CUpdateCategoryParamsDto } from './dto/update.dto';
import { CCreateSubCategoryDto } from './dto/create-subcategory.dto';
import { CGetSubCategoryListQueryDto } from './dto/list-subcategory.dto';
import { CGetSubCategoryDetailParamsDto } from './dto/detail-subcategory.dto';
import {
  CUpdateSubCategoryDto,
  CUpdateSubCategoryParamsDto,
} from './dto/update-subcategory.dto';
import { CExportSubCategoryQueryDto } from './dto/export-subcategory.dto';
import { CGetMasterCategoryQueryDto } from './dto/master-category.dto';

@Controller('/admin')
export class MasterCategoryController {
  constructor(private readonly masterCategoryService: MasterCategoryService) {}

  // Auth
  @Get('category-list')
  getCategoryList(@Query() query: CGetCategoryListQueryDto) {
    return this.masterCategoryService.getCategoryList(query);
  }

  // Auth
  @Get('category-detail/:id')
  getCategoryDetail(@Param() params: CGetCategoryDetailParamsDto) {
    return this.masterCategoryService.getCategoryDetail(params);
  }

  // Auth
  @Post('category-insert')
  async createCategory(@Body() body: CCreateCategoryDto) {
    return this.masterCategoryService.createCategory(body);
  }

  // // Auth
  @Post('category-update/:id')
  async updateCategory(
    @Param() params: CUpdateCategoryParamsDto,
    @Body() body: CUpdateCategoryDto,
  ) {
    return this.masterCategoryService.updateCategory(params, body);
  }

  // Auth
  @Get('category-export')
  async getExportCategory(
    @Query() query: CExportCategoryQueryDto,
    @Res() res: express.Response,
  ) {
    const response = await this.masterCategoryService.getExportCategory(query);

    const date = dayjs().format('DD-MM-YYYY');

    return res
      .set(
        'Content-Disposition',
        `attachment; filename=lemon_category_${date}.xlsx`,
      )
      .send(response);
  }

  // Subcategory

  // Auth
  @Get('subcategory-list')
  getSubCategoryList(@Query() query: CGetSubCategoryListQueryDto) {
    return this.masterCategoryService.getSubCategoryList(query);
  }

  // Auth
  @Get('subcategory-detail/:id')
  getSubCategoryDetail(@Param() params: CGetSubCategoryDetailParamsDto) {
    return this.masterCategoryService.getSubCategoryDetail(params);
  }

  // Auth
  @Post('subcategory-insert')
  async createSubCategory(@Body() body: CCreateSubCategoryDto) {
    return this.masterCategoryService.createSubCategory(body);
  }

  // // Auth
  @Post('subcategory-update/:id')
  async updateSubCategory(
    @Param() params: CUpdateSubCategoryParamsDto,
    @Body() body: CUpdateSubCategoryDto,
  ) {
    return this.masterCategoryService.updateSubCategory(params, body);
  }

  // Auth
  @Get('subcategory-export')
  async getExportSubCategory(
    @Query() query: CExportSubCategoryQueryDto,
    @Res() res: express.Response,
  ) {
    const response =
      await this.masterCategoryService.getExportSubCategory(query);

    const date = dayjs().format('DD-MM-YYYY');

    return res
      .set(
        'Content-Disposition',
        `attachment; filename=lemon_subcategory_${date}.xlsx`,
      )
      .send(response);
  }

  //   "select
  // id,
  // category_name,
  // from category
  // where is_deleted is false
  // order by category_name asc"	category	- search  //by category_name	-	-	"{
  // ""status"": true,
  // ""message"": ""success get category"",
  // ""data"":
  //   {
  //     ""id"": 1,
  //     ""category_name"": ""Finance""
  //   },
  //   {
  //     ""id"": 2,
  //     ""category_name"": ""Technology""
  //   }

  // }"	"//data not found
  // {
  // ""status"": false,
  // ""message"": ""data not found!"",
  // ""data"": {}
  // }"

  @Get('master/category')
  getMasterCategory(@Query() query: CGetMasterCategoryQueryDto) {
    return this.masterCategoryService.getMasterCategory(query);
  }
}
