import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetLogLoginListQueryDto,
  IGetLogLoginListResponseDto,
} from '../dto/list-log-login.dto';
import { getDuration } from 'src/common/utils/duration.util';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { Prisma } from '@prisma/client';

export const JGetLogLoginList = async (
  prisma: PrismaService,
  query: CGetLogLoginListQueryDto,
): Promise<IGlobalResponseDto<IGetLogLoginListResponseDto[]>> => {
  try {
    const { search, search_by, page = 1, limit = 10 } = query;

    const { skip, take } = getOffset(page, limit);

    const filter: Prisma.user_log_loginWhereInput = {};

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const [data, count] = await Promise.all([
      prisma.user_log_login.findMany({
        where: filter,
        orderBy: {
          ID: 'desc',
        },
        skip,
        take,
      }),
      prisma.user_log_login.count({ where: filter }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetLogLoginListResponseDto[] = data.map((item) => ({
      id: item.ID,
      npk: item.npk,
      start_date: item.start_date?.toISOString() ?? null,
      end_date: item.end_date?.toISOString() ?? null,
      duration: getDuration(item.start_date, item.end_date),
      created_date: item.created_date?.toISOString() ?? null,
      created_by: item.created_by,
      updated_date: item.updated_date?.toISOString() ?? null,
      updated_by: item.updated_by,
    }));

    return {
      status: true,
      message: 'success get data user log',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/log-login/list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
