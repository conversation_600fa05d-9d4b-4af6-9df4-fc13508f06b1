import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { CCreateCategoryDto } from '../dto/create.dto';
import { HttpStatus } from '@nestjs/common';

export const JCreateCategory = async (
  prisma: PrismaService,
  body: CCreateCategoryDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const isAlreadyExists = await prisma.category.findFirst({
      where: {
        OR: [{ is_deleted: false }, { is_deleted: null }],
        category_name: body.category_name,
      },
    });

    if (isAlreadyExists) {
      throw handleError({
        status: HttpStatus.CONFLICT,
        message: 'failed, category name must be unique',
      });
    }

    await prisma.category.create({
      data: {
        category_name: body.category_name,
        is_deleted: false,
        created_by: null,
        updated_by: null,
        created_at: new Date(),
        last_updated: new Date(),
      },
    });

    return {
      status: true,
      message: 'success insert data category',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
