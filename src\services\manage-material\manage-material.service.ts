import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CGetManageMaterialListQueryDto } from './dto/list.dto';
import { JGetManageMaterialList } from './jobs/list.job';
import { JGetManageMaterialDetail } from './jobs/detail.job';
import { CGetManageMaterialDetailParamsDto } from './dto/detail.dto';
import { CCreateMaterialBodyDto } from './dto/create.dto';
import { JCreateMaterial } from './jobs/create.job';
import {
  CUpdateMaterialBodyDto,
  CUpdateMaterialParamsDto,
} from './dto/update.dto';
import { JUpdateMaterial } from './jobs/update.job';
import { CGetPresignUrlQueryDto } from './dto/presign-url.dto';
import { JGetPresignMaterialUrl } from './jobs/get-presign-url.job';
import {
  CDeleteMaterialBodyDto,
  CDeleteMaterialParamsDto,
} from './dto/delete.dto';
import { JDeleteMaterial } from './jobs/delete.job';

@Injectable()
export class ManageMaterialService {
  constructor(private readonly prisma: PrismaService) {}

  getManageMaterialList = (query: CGetManageMaterialListQueryDto) =>
    JGetManageMaterialList(this.prisma, query);
  getManageMaterialDetail = (params: CGetManageMaterialDetailParamsDto) =>
    JGetManageMaterialDetail(this.prisma, params);
  getPresignedUrlMaterial = (query: CGetPresignUrlQueryDto) =>
    JGetPresignMaterialUrl(query);
  deleteMaterial = (
    params: CDeleteMaterialParamsDto,
    body: CDeleteMaterialBodyDto,
  ) => JDeleteMaterial(this.prisma, params, body);
  createMaterial = (body: CCreateMaterialBodyDto, file?: Express.Multer.File) =>
    JCreateMaterial(this.prisma, body, file);
  updateMaterial = (
    params: CUpdateMaterialParamsDto,
    body: CUpdateMaterialBodyDto,
    file?: Express.Multer.File,
  ) => JUpdateMaterial(this.prisma, params, body, file);
}
