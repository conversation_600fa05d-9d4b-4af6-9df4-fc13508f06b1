import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { CGetUserRoleQueryDto, IGetUserRoleResponseDto } from '../dto/role.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetUserRole = async (
  prisma: PrismaService,
  query: CGetUserRoleQueryDto,
): Promise<IGlobalResponseDto<IGetUserRoleResponseDto[]>> => {
  try {
    const { search } = query;

    const filter: Prisma.rolesWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search) {
      filter.AND = [
        {
          user_role_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.roles.findMany({
      where: filter,
      select: {
        id: true,
        user_role_name: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    if (!data.length) {
      return {
        status: true,
        message: 'no data found',
        data: [],
      };
    }

    const result: IGetUserRoleResponseDto[] = data.map((item) => ({
      role_id: item.id,
      role_name: item.user_role_name,
    }));

    return {
      status: true,
      message: 'success get data role user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
