import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  convertImageToWebp,
  removeExtension,
} from 'src/common/utils/image-processor.util';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import { CCreateSliderBodyDto, ISliderImage } from '../dto/create-slider.dto';
import dayjs from 'dayjs';

export const JCreateSlider = async (
  prisma: PrismaService,
  body: CCreateSliderBodyDto,
  file: ISliderImage,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const data = body.data;

    let slider_desktop: string | null = null;
    let slider_mobile: string | null = null;

    if (
      file?.slider_desktop &&
      file.slider_desktop.mimetype.startsWith('image/')
    ) {
      slider_desktop = await uploadSliderImage(file.slider_desktop);
    }

    if (
      file?.slider_mobile &&
      file.slider_mobile.mimetype.startsWith('image/')
    ) {
      slider_mobile = await uploadSliderImage(file.slider_mobile);
    }

    await prisma.sliders.create({
      data: {
        slider_name: data.slider_name,
        link: data.link,
        slider: slider_desktop,
        slider_mobile: slider_mobile,
      },
    });

    return {
      status: true,
      message: 'success insert data slider',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

async function uploadSliderImage(file: Express.Multer.File) {
  let buffer = file.buffer;
  let filePathName = file.originalname;
  const mimetype = 'image/webp';

  buffer = await convertImageToWebp(buffer);
  filePathName = `sliders/${dayjs().unix()}-${removeExtension(filePathName)}.webp`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': mimetype,
  });

  return `lemon/${filePathName}`;
}
