import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { HttpStatus } from '@nestjs/common';
import { CCreateSubCategoryDto } from '../dto/create-subcategory.dto';

export const JCreateSubCategory = async (
  prisma: PrismaService,
  body: CCreateSubCategoryDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const isAlreadyExists = await prisma.sub_category.findFirst({
      where: {
        OR: [{ is_deleted: false }, { is_deleted: null }],
        sub_category_name: body.subcategory_name,
      },
    });

    if (isAlreadyExists) {
      throw handleError({
        status: HttpStatus.CONFLICT,
        message: 'failed, subcategory name must be unique',
      });
    }

    await prisma.sub_category.create({
      data: {
        sub_category_name: body.subcategory_name,
        category_id: body.category_id,
        is_deleted: false,
        created_by: null,
        updated_by: null,
        created_at: new Date(),
        last_updated: new Date(),
      },
    });

    return {
      status: true,
      message: 'success insert data subcategory',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
