import { createZodDto } from 'nestjs-zod';
import { zBooleanOptional } from 'src/common/utils/validation.util';
import z from 'zod';

const updateLearningLevelParamsSchema = z.object({
  id: z.coerce.number(),
});

export class CUpdateLearningLevelParamsDto extends createZodDto(
  updateLearningLevelParamsSchema,
) {}

const updateLearningLevelBodySchema = z.object({
  name: z.string().optional(),
  level: z.number().optional(),
  status: z.enum(['active', 'inactive']).optional(),
  is_deleted: zBooleanOptional(),
});

export class CUpdateLearningLevelBodyDto extends createZodDto(
  updateLearningLevelBodySchema,
) {}
