import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getImageRepositoryDetailParams = z.object({ id: z.coerce.number() });

export class CGetImageRepositoryDetailParamsDto extends createZodDto(
  getImageRepositoryDetailParams,
) {}

export interface IImageRepositoryDetailResponseDto {
  id: number;
  image_name: string | null;
  file_format: string | null;
  file_size: number | null;
  link: string | null;
  categories: { id: number | null; name: string | null }[];
  levels: { id: number | null; name: string | null }[];
  associated: { question_id: number | null; question: string | null }[];
  feature: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
