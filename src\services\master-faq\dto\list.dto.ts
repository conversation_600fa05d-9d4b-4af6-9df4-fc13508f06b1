import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

const getFAQListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z.enum(['question', 'answer']).optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetFAQListQueryDto extends createZodDto(getFAQListQuerySchema) {}

export interface IGetFAQListResponseDto {
  id: number;
  question: string | null;
  answer: string | null;
}
