import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CCreateJobPositionDto } from './dto/create.dto';
import { JCreateJobPosition } from './jobs/create.job';
import {
  CUpdateJobPositionDto,
  CUpdateJobPositionParamsDto,
} from './dto/update.dto';
import { JUpdateJobPosition } from './jobs/update.job';
import { CGetJobPositionListQueryDto } from './dto/list.dto';
import { JGetJobPositionList } from './jobs/list.job';
import { JGetJobPositionDetail } from './jobs/detail.job';
import { CGetJobPositionDetailParamsDto } from './dto/detail.dto';
import { CGetJobFunctionListQueryDto } from './dto/list-function.dto';
import { JGetJobFunctionList } from './jobs/list-function.job';
import { CGetDepartmentListQueryDto } from './dto/list-department.dto';
import { JGetDepartmentList } from './jobs/list.department.job';
import { CGetStartingLevelListQueryDto } from './dto/list-starting-level.dto';
import { JGetStartingLevelList } from './jobs/list-starting-level.job';
import { CExportJobPositionQueryDto } from './dto/export.dto';
import { JExportJobPositionList } from './jobs/export.job';

@Injectable()
export class JobPositionService {
  constructor(private readonly prisma: PrismaService) {}

  getJobPositionList = (query: CGetJobPositionListQueryDto) =>
    JGetJobPositionList(this.prisma, query);
  getJobPositionDetail = (params: CGetJobPositionDetailParamsDto) =>
    JGetJobPositionDetail(this.prisma, params);
  createJobPosition = (body: CCreateJobPositionDto) =>
    JCreateJobPosition(this.prisma, body);
  updateJobPosition = (
    params: CUpdateJobPositionParamsDto,
    body: CUpdateJobPositionDto,
  ) => JUpdateJobPosition(this.prisma, params, body);
  getExportJobPosition = (query: CExportJobPositionQueryDto) =>
    JExportJobPositionList(this.prisma, query);
  getJobFunctionList = (query: CGetJobFunctionListQueryDto) =>
    JGetJobFunctionList(this.prisma, query);
  getDepartmentList = (query: CGetDepartmentListQueryDto) =>
    JGetDepartmentList(this.prisma, query);
  getStartingLevelList = (query: CGetStartingLevelListQueryDto) =>
    JGetStartingLevelList(this.prisma, query);
}
