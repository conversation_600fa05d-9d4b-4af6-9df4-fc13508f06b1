import { HttpException, HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from '../dto/response.dto';

export const handleError = (error: { status: number; message: string }) => {
  const errorResponse: IGlobalResponseDto = {
    status: false,
    message: error.message ?? 'Something went wrong',
    data: [],
  };

  console.error(errorResponse);
  return new HttpException(
    errorResponse,
    error.status ?? HttpStatus.INTERNAL_SERVER_ERROR,
  );
};
