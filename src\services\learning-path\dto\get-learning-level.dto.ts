import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import z from 'zod';

const getLearningLevelListQuerySchema = z
  .object({
    search: z.string().optional(),
    search_by: z
      .enum(['level', 'name', 'modul_name', 'created_by', 'updated_by'])
      .optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetLearningLevelListQueryDto extends createZodDto(
  getLearningLevelListQuerySchema,
) {}

export interface IGetLearningLevelListResponseDto {
  id: number | null;
  name: string | null;
  level: number | null;
  status: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}

const getLearningLevelDetailParamsSchema = z.object({
  id: z.number(),
});

export class CGetLearningLevelDetailParamsDto extends createZodDto(
  getLearningLevelDetailParamsSchema,
) {}

export interface IGetLearningLevelDetailResponseDto {
  id: number | null;
  name: string | null;
  level: number | null;
  status: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
