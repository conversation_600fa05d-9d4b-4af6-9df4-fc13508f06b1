import { PrismaService } from 'src/services/prisma/prisma.service';
import { CGetUserDownloadQueryDto } from '../dto/download.dto';
import { Prisma } from '@prisma/client';
import { handleError } from 'src/common/utils/error.util';
import { Workbook } from 'exceljs';

export const JGetUserDownload = async (
  prisma: PrismaService,
  query: CGetUserDownloadQueryDto,
) => {
  try {
    const {
      entity_id,
      is_neop,
      is_new_user,
      role_id,
      search,
      search_by,
      type_id,
    } = query;

    const filter: Prisma.user_detailsWhereInput = {
      is_deleted: false,
      entity_id,
      is_need_neop: is_neop,
      is_new_user: is_new_user,
      user_role_id: role_id,
      user_type_id: type_id,
    };

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
          },
        },
      ];
    }

    const data = await prisma.user_details.findMany({
      where: filter,
      select: {
        ID: true,
        name: true,
        job_name: true,
        email: true,
        second_email: true,
        phone_number: true,
        user_type_id: true,
        npk: true,
        user_type: {
          select: {
            user_type_name: true,
          },
        },
        user_role_id: true,
        roles: {
          select: {
            user_role_name: true,
          },
        },
        entity_id: true,
        entity: {
          select: {
            entity_name: true,
          },
        },
        is_need_neop: true,
        is_need_welcoming_kit: true,
        updated_at: true,
        updated_by: true,
        is_active: true,
        is_new_user: true,
      },
      orderBy: {
        updated_at: 'desc',
      },
    });

    const payload: ICreateFileData[] = data.map((item) => ({
      id: item.ID ?? '-',
      npk: item.npk ?? '-',
      name: item.name ?? '-',
      job_name: item.job_name ?? '-',
      email: item.email ?? '-',
      secondary_email: item.second_email ?? '-',
      phone_number: item.phone_number ?? '-',
      user_type: item.user_type?.user_type_name ?? '-',
      user_role: item.roles?.user_role_name ?? '-',
      supervisor_id: '-',
      supervisor_name: '-',
      supervisor_npk: '-',
      is_need_neop: item.is_need_neop ? 'Yes' : 'No',
      is_need_welcoming_kit: item.is_need_welcoming_kit ? 'Yes' : 'No',
      updated_by: item.updated_by ?? '-',
      status: item.is_active ? 'Active' : 'Inactive',
    }));

    return await createFile(payload, is_new_user);
  } catch (error: any) {
    throw handleError(error);
  }
};

const createFile = async (data: ICreateFileData[], isNewUser: boolean) => {
  const workbook = new Workbook();
  const worksheetName = isNewUser ? 'New User' : 'Regular User';
  const ws = workbook.addWorksheet(worksheetName);

  // 1) Definisikan columns (pakai header & key seperti semula)
  const columns = [
    { header: 'User ID', key: 'id' },
    { header: 'NPK', key: 'npk' },
    { header: 'Name', key: 'name' },
    { header: 'Job Position', key: 'job_name' },
    { header: 'Primary Email', key: 'email' },
    { header: 'Secondary Email', key: 'secondary_email' },
    { header: 'Phone Number', key: 'phone_number' },
    { header: 'User Type', key: 'user_type' },
    { header: 'User Role', key: 'user_role' },
    { header: 'Supervisor ID', key: 'supervisor_id' },
    { header: 'Supervisor Name', key: 'supervisor_name' },
    { header: 'Supervisor NPK', key: 'supervisor_npk' },
    { header: 'Need NEOP', key: 'is_need_neop' },
    { header: 'Need Welcoming Kit', key: 'is_need_welcoming_kit' },
    { header: 'Updated By', key: 'updated_by' },
    { header: 'Status', key: 'status' },
  ];
  ws.columns = columns;

  // 2) Kosongkan row-1 (exceljs menulis header di sini secara default)
  ws.getRow(1).values = new Array(columns.length + 1).fill(null);

  // 3) Row 1: Title (merged)
  ws.mergeCells(1, 1, 1, columns.length);
  const titleCell = ws.getCell(1, 1);
  titleCell.value = worksheetName; // <-- tidak lagi jadi "Status"
  titleCell.alignment = { horizontal: 'center' };
  titleCell.font = { bold: true };

  // 4) Row 2: kosong (spacer)
  ws.insertRow(2, []);

  // 5) Row 3: Header manual (bold)
  const headerRow = ws.getRow(3);
  columns.forEach((c, idx) => {
    const cell = headerRow.getCell(idx + 1);
    cell.value = c.header;
    cell.font = { bold: true };
    cell.alignment = { horizontal: 'center' };
  });
  headerRow.commit();

  // 6) Data mulai baris 4
  data.forEach((val) => ws.addRow(val));

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};

interface ICreateFileData {
  id: number | null;
  npk: string | null;
  name: string | null;
  job_name: string | null;
  email: string | null;
  secondary_email: string | null;
  phone_number: string | null;
  user_type: string | null;
  user_role: string | null;
  supervisor_id: string | null;
  supervisor_name: string | null;
  supervisor_npk: string | null;
  is_need_neop: string | null;
  is_need_welcoming_kit: string | null;
  updated_by: string | null;
  status: string | null;
}
