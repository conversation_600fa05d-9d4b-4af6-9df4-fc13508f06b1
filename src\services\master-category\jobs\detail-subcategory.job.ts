import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetSubCategoryDetailParamsDto,
  IGetSubCategoryDetailResponseDto,
} from '../dto/detail-subcategory.dto';

export const JGetSubCategoryDetail = async (
  prisma: PrismaService,
  params: CGetSubCategoryDetailParamsDto,
): Promise<IGlobalResponseDto<IGetSubCategoryDetailResponseDto>> => {
  try {
    const { id } = params;

    const data = await prisma.sub_category
      .findUniqueOrThrow({
        where: {
          id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        select: {
          id: true,
          sub_category_name: true,
          category: true,
          created_at: true,
          created_by: true,
          last_updated: true,
          updated_by: true,
        },
      })
      .catch(() => {
        throw handleError({
          message: 'Subcategory not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IGetSubCategoryDetailResponseDto = {
      id: data.id,
      subcategory_name: data.sub_category_name,
      category_id: data.category?.id ?? null,
      category_name: data.category?.category_name ?? null,
      created_by: data.created_by,
      updated_by: data.updated_by,
      created_at: data.created_at?.toISOString() ?? null,
      last_updated: data.last_updated?.toISOString() ?? null,
    };

    return {
      status: true,
      message: 'success get data subcategory',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
