import { Controller, Get, Query, Res } from '@nestjs/common';
import { LogManagementService } from './log-management.service';
import { CGetLogLoginListQueryDto } from './dto/list-log-login.dto';
import * as express from 'express';
import dayjs from 'dayjs';
import { CGetExportLogLoginListQueryDto } from './dto/export.log-login.dto';

@Controller('/admin')
export class LogManagementController {
  constructor(private readonly LogLoginService: LogManagementService) {}

  // Auth
  @Get('log-login/list')
  getLogLoginList(@Query() query: CGetLogLoginListQueryDto) {
    return this.LogLoginService.getLogLoginList(query);
  }

  // Auth
  @Get('log-login/export')
  async getExportLogLogin(
    @Query() query: CGetExportLogLoginListQueryDto,
    @Res() res: express.Response,
  ) {
    const response = await this.LogLoginService.getExportLogLogin(query);

    const date = dayjs().format('DD-MM-YYYY');

    return res
      .set(
        'Content-Disposition',
        `attachment; filename=userLogLogin_${date}.xlsx`,
      )
      .send(response);
  }
}
