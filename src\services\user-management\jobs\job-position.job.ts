import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetJobPositionQueryDto,
  IGetJobPositionResponseDto,
} from '../dto/job-position.dto';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetJobPosition = async (
  prisma: PrismaService,
  query: CGetJobPositionQueryDto,
): Promise<IGlobalResponseDto<IGetJobPositionResponseDto[]>> => {
  try {
    const { search } = query;

    const filter: Prisma.job_namesWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    if (search) {
      filter.AND = [
        {
          job_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const data = await prisma.job_names.findMany({
      where: filter,
      select: {
        ID: true,
        job_name: true,
      },
      orderBy: {
        job_name: 'asc',
      },
    });

    if (!data.length) {
      return {
        status: true,
        message: 'no data found',
        data: [],
      };
    }

    const result: IGetJobPositionResponseDto[] = data.map((item) => ({
      job_id: item.ID,
      job_name: item.job_name,
    }));

    return {
      status: true,
      message: 'success get data entity user',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
