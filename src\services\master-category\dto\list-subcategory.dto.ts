import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

export const getSubCategoryListQuerySchema = z
  .object({
    search: z.union([z.string(), z.number(), z.date()]).optional(),
    search_by: z
      .enum([
        'sub_category_id',
        'sub_category_name',
        'category_name',
        'created_by',
        'created_at',
      ])
      .optional(),
  })
  .refine(
    (data) => {
      if (data.search_by === 'sub_category_id') {
        return (
          data.search === undefined ||
          typeof data.search === 'number' ||
          !isNaN(Number(data.search))
        );
      }
      if (data.search_by === 'created_at') {
        return (
          data.search === undefined ||
          data.search instanceof Date ||
          !isNaN(Date.parse(String(data.search)))
        );
      }
      return true;
    },
    {
      message:
        'search must be number if search_by=sub_category_id, or date if search_by=created_at',
      path: ['search'],
    },
  )
  .transform((data) => {
    if (
      data.search_by === 'sub_category_id' &&
      typeof data.search === 'string'
    ) {
      return { ...data, search: Number(data.search) };
    }
    if (data.search_by === 'created_at' && typeof data.search === 'string') {
      return { ...data, search: new Date(data.search) };
    }
    return data;
  })
  .and(globalPaginationQuerySchema);

export class CGetSubCategoryListQueryDto extends createZodDto(
  getSubCategoryListQuerySchema,
) {}

export interface IGetSubCategoryListResponseDto {
  id: number;
  subcategory_name: string | null;
  category_id: number | null;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
