import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  CGetLearningCodeDetailParamsDto,
  CGetLearningCodeListQueryDto,
} from './dto/get-learning-code.dto';
import { JGetLearningCodeList } from './jobs/get-learning-code.job';
import { JGetLearningCodeDetail } from './jobs/get-detail-code.job';
import { CCreateLearningCodeBodyDto } from './dto/create-learning-code.dto';
import { JCreateLearningCode } from './jobs/create-learning-code.dto';
import {
  CUpdateLearningCodeBodyDto,
  CUpdateLearningCodeParamsDto,
} from './dto/update-learning-code.dto';
import { JUpdateLearningCode } from './jobs/update-learning-code.dto';
import {
  CGetLearningLevelDetailParamsDto,
  CGetLearningLevelListQueryDto,
} from './dto/get-learning-level.dto';
import { JGetLearningLevelList } from './jobs/get-learning-level.job';
import { JGetLearningLevelDetail } from './jobs/get-detail-level.job';
import { JCreateLearningLevel } from './jobs/create-learning-level.dto';
import { CCreateLearningLevelBodyDto } from './dto/create-learning-level.dto';
import {
  CUpdateLearningLevelBodyDto,
  CUpdateLearningLevelParamsDto,
} from './dto/update-learning-level.dto';
import { JUpdateLearningLevel } from './jobs/update-learning-level.dto';
import { CGetCertificateListQueryDto } from './dto/get-certificate.dto';
import { JGetCertificateList } from './jobs/get-certificate.job';

@Injectable()
export class LearningPathService {
  constructor(private readonly prisma: PrismaService) {}

  getLearningCodeList = (query: CGetLearningCodeListQueryDto) =>
    JGetLearningCodeList(this.prisma, query);
  getLearningCodeDetail = (params: CGetLearningCodeDetailParamsDto) =>
    JGetLearningCodeDetail(this.prisma, params);
  createLearningCode = (body: CCreateLearningCodeBodyDto) =>
    JCreateLearningCode(this.prisma, body);
  updateLearningCode = (
    params: CUpdateLearningCodeParamsDto,
    body: CUpdateLearningCodeBodyDto,
  ) => JUpdateLearningCode(this.prisma, params, body);
  getLearningLevelList = (query: CGetLearningLevelListQueryDto) =>
    JGetLearningLevelList(this.prisma, query);
  getLearningLevelDetail = (params: CGetLearningLevelDetailParamsDto) =>
    JGetLearningLevelDetail(this.prisma, params);
  createLearningLevel = (body: CCreateLearningLevelBodyDto) =>
    JCreateLearningLevel(this.prisma, body);
  updateLearningLevel = (
    params: CUpdateLearningLevelParamsDto,
    body: CUpdateLearningLevelBodyDto,
  ) => JUpdateLearningLevel(this.prisma, params, body);
  getCertificateList = (query: CGetCertificateListQueryDto) =>
    JGetCertificateList(this.prisma, query);
}
