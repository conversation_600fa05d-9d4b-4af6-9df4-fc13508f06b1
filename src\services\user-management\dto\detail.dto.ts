import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getUserDetailParams = z.object({ id: z.coerce.number() });

export class CGetUserDetailParamsDto extends createZodDto(
  getUserDetailParams,
) {}

export interface IGetUserDetailResponseDto {
  id: number | null;
  npk: string | null;
  name: string | null;
  job_name_id: number | null;
  job_name: string | null;
  email: string | null;
  second_email: string | null;
  phone_number: string | null;
  supervisor_id: number | null;
  supervisor_name: string | null;
  user_type_id: number | null;
  user_type_name: string | null;
  user_role_id: number | null;
  user_role_name: string | null;
  entity_id: number | null;
  entity_name: string | null;
  forum_title_id: number | null;
  forum_title_name: string | null;
  is_need_neop: boolean | null;
  is_need_welcoming_kit: boolean | null;
  updated_at: Date | null;
  updated_by: string | null;
  is_active: boolean | null;
  signature: string | null;
}
