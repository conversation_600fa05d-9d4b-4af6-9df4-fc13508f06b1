import {
  CanActivate,
  ExecutionContext,
  Injectable,
  HttpStatus,
} from '@nestjs/common';
import { handleError } from '../utils/error.util';

@Injectable()
export class FormDataGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const contentType = request.headers['content-type'];

    if (!contentType || !contentType.includes('multipart/form-data')) {
      throw handleError({
        message: 'Content-Type must be multipart/form-data',
        status: HttpStatus.BAD_REQUEST,
      });
    }

    return true;
  }
}
