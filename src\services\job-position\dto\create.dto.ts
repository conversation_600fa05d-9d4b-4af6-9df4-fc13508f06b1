import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const createJobPositionSchema = z.object({
  job_id: z.string().min(1, 'Job id cannot be empty'),
  job_name: z.string().optional(),
  job_position_type: z.string().optional(),
  department_id: z.string().optional(),
  department_name: z.string().optional(),
  job_function_id: z.number().optional(),
  job_function: z.string().optional(),
  is_active: z.boolean().optional(),
  level: z.number().optional(),
  level_id: z.number().optional(),
  level_name: z.string().optional(),
  ho_dept: z.string().optional(),
  entity_id: z.number().optional(),
  is_need_neop: z.boolean().optional(),
  is_need_welcoming_kit: z.boolean().optional(),
  starter_module_priority: z.string().optional(),
});

export class CCreateJobPositionDto extends createZodDto(
  createJobPositionSchema,
) {}
