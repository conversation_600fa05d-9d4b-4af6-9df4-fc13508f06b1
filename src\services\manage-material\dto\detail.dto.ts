import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getManageMaterialDetailParams = z.object({ id: z.coerce.number() });

export class CGetManageMaterialDetailParamsDto extends createZodDto(
  getManageMaterialDetailParams,
) {}

export interface IManageMaterialDetailResponseDto {
  id: number;
  type: string | null;
  name: string | null;
  file_format: string | null;
  levels: {
    id: number | null;
    level: string | null;
  }[];
  categories: {
    id: number | null;
    name: string | null;
  }[];
  associated: {
    section_id: number | null;
    section_name: string | null;
    categories: {
      section_category_id: number | null;
      section_category_name: string | null;
    }[];
    levels: {
      section_level_id: number | null;
      section_level_name: string | null;
    }[];
  }[];
  link: string | null;
  filesize: number | null;
  feature: string | null;
  created_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  last_updated: string | null;
}
