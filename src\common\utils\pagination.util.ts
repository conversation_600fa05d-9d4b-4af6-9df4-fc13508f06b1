import { IGlobalPaginationDto } from '../dto/pagination.dto';

export const getOffset = (
  page: number | undefined,
  limit: number | undefined,
): { skip: number | undefined; take: number | undefined } => {
  return {
    skip: limit && page ? limit * (page - 1) : undefined,
    take: limit || undefined,
  };
};

export const createPagination = (
  currentPage: number,
  pageSize: number,
  totalRecords: number,
  path: string,
  query: Record<string, any> = {},
): IGlobalPaginationDto => {
  const totalPage = Math.ceil(totalRecords / pageSize);

  const nextPage = currentPage < totalPage ? currentPage + 1 : null;
  const prevPage = currentPage > 1 ? currentPage - 1 : null;

  const buildUrl = (page: number | null): string | null => {
    const queryParams = new URLSearchParams(query);

    if (page) queryParams.set('page', page.toString());

    return `${path}?${queryParams.toString()}`;
  };

  return {
    current_page: currentPage,
    total_page: totalPage,
    total_data: totalRecords,
    next: buildUrl(nextPage),
    prev: buildUrl(prevPage),
  };
};
