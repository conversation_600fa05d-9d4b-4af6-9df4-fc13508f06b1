import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetLearningLevelListQueryDto,
  IGetLearningLevelListResponseDto,
} from '../dto/get-learning-level.dto';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { Prisma } from '@prisma/client';

export const JGetLearningLevelList = async (
  prisma: PrismaService,
  query: CGetLearningLevelListQueryDto,
): Promise<IGlobalResponseDto<IGetLearningLevelListResponseDto[]>> => {
  try {
    const { limit, page, search, search_by } = query;

    const { skip, take } = getOffset(page, limit);

    const filter: Prisma.levelsWhereInput = {
      is_deleted: false,
    };

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const [data, count] = await Promise.all([
      prisma.levels.findMany({
        where: filter,
        orderBy: {
          level: 'asc',
        },
        select: {
          ID: true,
          name: true,
          level: true,
          status: true,
          created_at: true,
          created_by: true,
          updated_by: true,
          last_updated: true,
        },
        skip,
        take,
      }),
      prisma.levels.count({
        where: filter,
      }),
    ]);

    const result: IGetLearningLevelListResponseDto[] = data.map((item) => ({
      id: item.ID,
      name: item.name,
      level: item.level,
      status: item.status,
      created_at: item.created_at ? item.created_at.toISOString() : null,
      created_by: item.created_by,
      updated_by: item.updated_by,
      last_updated: item.last_updated ? item.last_updated.toISOString() : null,
    }));

    return {
      status: true,
      message: 'success get learning level',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/learning/level/list',
        query,
      ),
    };
  } catch (error) {
    throw handleError(error);
  }
};
