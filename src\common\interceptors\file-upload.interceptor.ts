import {
  FileInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { applyDecorators, HttpStatus, UseInterceptors } from '@nestjs/common';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { handleError } from '../utils/error.util';

interface FileUploadOptions {
  fieldName?: string; // default 'file'
  maxSizeMB?: number;
  allowedMimeTypes?: RegExp;
}

export function FileUploadInterceptor(options?: FileUploadOptions) {
  const fieldName = options?.fieldName || 'file';
  const maxSizeMB = options?.maxSizeMB || 5;
  const allowedMimeTypes = options?.allowedMimeTypes;

  const multerOptions: MulterOptions = {
    fileFilter: (_, file, cb) => {
      if (allowedMimeTypes && !file.mimetype.match(allowedMimeTypes)) {
        return cb(
          handleError({
            status: HttpStatus.BAD_REQUEST,
            message: `Invalid file type! Allowed: ${allowedMimeTypes}`,
          }),
          false,
        );
      }
      cb(null, true);
    },
    limits: { fileSize: maxSizeMB * 1024 * 1024 },
  };

  return applyDecorators(
    UseInterceptors(FileInterceptor(fieldName, multerOptions)),
  );
}

interface MultiFileUploadOptions {
  fields: { name: string; maxCount?: number }[];
  maxSizeMB?: number;
  allowedMimeTypes?: RegExp;
}

export function MultiFileUploadInterceptor(options: MultiFileUploadOptions) {
  const maxSizeMB = options?.maxSizeMB || 5;
  const allowedMimeTypes = options?.allowedMimeTypes;

  const multerOptions: MulterOptions = {
    fileFilter: (_, file, cb) => {
      if (allowedMimeTypes && !file.mimetype.match(allowedMimeTypes)) {
        return cb(
          handleError({
            status: HttpStatus.BAD_REQUEST,
            message: `Invalid file type! Allowed: ${allowedMimeTypes}`,
          }),
          false,
        );
      }
      cb(null, true);
    },
    limits: { fileSize: maxSizeMB * 1024 * 1024 },
  };

  return applyDecorators(
    UseInterceptors(FileFieldsInterceptor(options.fields, multerOptions)),
  );
}
