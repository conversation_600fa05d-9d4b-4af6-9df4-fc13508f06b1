import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetPresignUrlQueryDto,
  IGetPresignUrlResponseDto,
} from '../dto/presign-url.dto';
import { getPresignedUrlMinio } from 'src/common/utils/minio.util';

export const JGetPresignMaterialUrl = async (
  query: CGetPresignUrlQueryDto,
): Promise<IGlobalResponseDto<IGetPresignUrlResponseDto>> => {
  try {
    const { url } = query;

    // expires in 1 hour
    const presignUrl = await getPresignedUrlMinio(url, 3600);

    const result: IGetPresignUrlResponseDto = {
      filename: url?.split('/')?.pop() ?? url,
      url: presignUrl,
    };

    return {
      status: true,
      message: 'success generate url',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
