import { createZodDto } from 'nestjs-zod';
import z from 'zod';

export const createSliderSchema = z.object({
  data: z.preprocess(
    (val) => {
      if (typeof val === 'string') {
        try {
          return JSON.parse(val);
        } catch {
          return val;
        }
      }
      return val;
    },
    z.object({
      slider_name: z.string().min(1, 'Slider Name cannot be empty'),
      link: z.string().optional(),
    }),
  ),
});

export class CCreateSliderBodyDto extends createZodDto(createSliderSchema) {}

export interface ISliderImage {
  slider_desktop?: Express.Multer.File;
  slider_mobile?: Express.Multer.File;
}
