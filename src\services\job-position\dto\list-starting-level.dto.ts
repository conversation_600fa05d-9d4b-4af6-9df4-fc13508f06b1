import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const getStartingLevelListQuerySchema = z.object({
  order: z.enum(['asc', 'desc']).optional(),
  order_by: z.enum(['id', 'name', 'level']).optional(),
});

export class CGetStartingLevelListQueryDto extends createZodDto(
  getStartingLevelListQuerySchema,
) {}

export interface IGetStartingLevelListResponseDto {
  id: number;
  name: string | null;
  level: number | null;
}
