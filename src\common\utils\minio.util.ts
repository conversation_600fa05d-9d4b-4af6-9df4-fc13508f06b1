import * as Minio from 'minio';
import env from '../configs/env.config';
import { ClientOptions, ItemBucketMetadata } from 'minio';

export const minioStorageConfig: ClientOptions = {
  endPoint: env.MINIO.ENDPOINT,
  port: env.MINIO.PORT,
  useSSL: env.MINIO.SSL,
  accessKey: env.MINIO.ACCESS_KEY,
  secretKey: env.MINIO.SECRET_KEY,
};

export async function uploadFileToMinio(
  objectName: string,
  fileContent: Buffer | string,
  metadata?: ItemBucketMetadata,
): Promise<void> {
  const minioClient = new Minio.Client(minioStorageConfig);

  let streamSize: number | undefined = undefined;

  if (typeof fileContent === 'string') {
    fileContent = Buffer.from(fileContent);
    streamSize = fileContent.length;
  } else if (fileContent instanceof Buffer) {
    streamSize = fileContent.length;
  }

  try {
    await minioClient.putObject(
      env.MINIO.BUCKETNAME,
      objectName,
      fileContent,
      streamSize,
      metadata,
    );
  } catch (error) {
    console.error(`Error upload file '${objectName}' to Minio:`, error);
    throw new Error(
      `failed to upload file to Minio: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function deleteFileFromMinio(fileName: string): Promise<void> {
  const minioClient = new Minio.Client(minioStorageConfig);

  try {
    await minioClient.removeObject(env.MINIO.BUCKETNAME, fileName);
  } catch (error) {
    console.error(`Error deleting file from Minio for '${fileName}':`, error);
    throw new Error(
      `Failed to delete file from Minio: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function getFileFromMinio(fileName: string): Promise<Buffer> {
  const minioClient = new Minio.Client(minioStorageConfig);

  try {
    const dataStream = await minioClient.getObject(
      env.MINIO.BUCKETNAME,
      fileName,
    );
    const chunks: Buffer[] = [];

    for await (const chunk of dataStream) {
      chunks.push(chunk as Buffer);
    }

    const fileBuffer = Buffer.concat(chunks);
    return fileBuffer;
  } catch (error) {
    console.error(`Error fetching file from Minio for '${fileName}':`, error);
    throw new Error(
      `Failed to load file from Minio: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function getObjectMinio(fileName: string) {
  const minioClient = new Minio.Client(minioStorageConfig);

  try {
    const dataStream = await minioClient.getObject(
      env.MINIO.BUCKETNAME,
      fileName,
    );
    return dataStream;
  } catch (error) {
    console.error(`Error fetching file from Minio for '${fileName}':`, error);
    throw new Error(
      `Failed to load file from Minio: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function getPresignedUrlMinio(
  filePathName: string,
  expiryInSecond: number,
) {
  const minioClient = new Minio.Client(minioStorageConfig);

  try {
    const presignedUrl = await minioClient.presignedGetObject(
      env.MINIO.BUCKETNAME,
      filePathName,
      expiryInSecond,
    );

    return presignedUrl;
  } catch (error) {
    console.error(
      `Error generating presigned from Minio for '${filePathName}':`,
      error,
    );
    throw new Error(
      `Failed to load file from Minio: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function getJsonFromMinio<T = any>(fileName: string): Promise<T> {
  const fileBuffer = await getFileFromMinio(fileName);
  const fileContent = fileBuffer.toString('utf8');
  try {
    const jsonData: T = JSON.parse(fileContent);
    return jsonData;
  } catch (error) {
    console.error(
      `Error parsing JSON content for object '${fileName}':`,
      error,
    );
    throw new Error(
      `Failed to parse JSON from file: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
