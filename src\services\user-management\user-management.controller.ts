import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import { CGetUserListQueryDto } from './dto/list.dto';
import { CGetUserListQueryDto as CGetUserListQueryDto2 } from './dto/user-list.dto';
import { CGetUserDetailParamsDto } from './dto/detail.dto';
import { CGetUserRoleQueryDto } from './dto/role.dto';
import { CGetUserTypeQueryDto } from './dto/type.dto';
import { CGetUserEntityQueryDto } from './dto/entity.dto';
import { CGetJobPositionQueryDto } from './dto/job-position.dto';
import { CGetForumTitleQueryDto } from './dto/forum-title.dto';
import * as express from 'express';
import { CGetUserDownloadQueryDto } from './dto/download.dto';
import dayjs from 'dayjs';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { createUserBodySchema } from './dto/create.dto';
import { handleError } from 'src/common/utils/error.util';
import { CUpdateUserParamsDto, updateUserBodySchema } from './dto/update.dto';

@Controller('/admin')
export class UserManagementController {
  constructor(private readonly userManagementService: UserManagementService) {}

  // Auth
  @Get('user-list')
  getAll(@Query() query: CGetUserListQueryDto) {
    return this.userManagementService.getAll(query);
  }

  // Auth
  @Get('user-detail/:id')
  getDetail(@Param() params: CGetUserDetailParamsDto) {
    return this.userManagementService.getDetail(params);
  }

  @Get('master/user-role')
  getMasterUserRole(@Query() query: CGetUserRoleQueryDto) {
    return this.userManagementService.getUserRole(query);
  }

  @Get('master/user-type')
  getMasterUserType(@Query() query: CGetUserTypeQueryDto) {
    return this.userManagementService.getUserType(query);
  }

  @Get('master/user-entity')
  getMasterUserEntity(@Query() query: CGetUserEntityQueryDto) {
    return this.userManagementService.getUserEntity(query);
  }

  @Get('master/job-position')
  getMasterJobPosition(@Query() query: CGetJobPositionQueryDto) {
    return this.userManagementService.getJobPosition(query);
  }

  // Auth
  @Get('master/user-list')
  getMasterUserList(@Query() query: CGetUserListQueryDto2) {
    return this.userManagementService.getUserList(query);
  }

  @Get('master/forum-title')
  getMasterForumTitle(@Query() query: CGetForumTitleQueryDto) {
    return this.userManagementService.getForumTitle(query);
  }

  @Get('user-download')
  async getUserDownload(
    @Query() query: CGetUserDownloadQueryDto,
    @Res() res: express.Response,
  ) {
    const response = await this.userManagementService.getUserDownload(query);

    const date = dayjs().format('DDMMYYYY');
    const fileName = query.is_new_user ? 'new_user' : 'regular_user';

    return res
      .set(
        'Content-Disposition',
        `attachment; filename=${fileName}_${date}.xlsx`,
      )
      .send(response);
  }

  @Post('user-insert')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(), // <— penting: pakai buffer
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: (_req, file, cb) => {
        const ok = /image\/(jpe?g|png|webp)/i.test(file.mimetype);
        cb(ok ? null : new Error('Invalid file type'), ok);
      },
    }),
  )
  createUser(
    @UploadedFile() file: Express.Multer.File,
    @Body('data') data: string,
  ) {
    try {
      const parsed = createUserBodySchema.parse(JSON.parse(data));

      return this.userManagementService.createUser(parsed, file);
    } catch (error: any) {
      throw handleError(error);
    }
  }
  @Post('user-update/:id')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(), // <— penting: pakai buffer
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: (_req, file, cb) => {
        const ok = /image\/(jpe?g|png|webp)/i.test(file.mimetype);
        cb(ok ? null : new Error('Invalid file type'), ok);
      },
    }),
  )
  updateUser(
    @UploadedFile() file: Express.Multer.File,
    @Body('data') data: string,
    @Param() params: CUpdateUserParamsDto,
  ) {
    try {
      const parsed = updateUserBodySchema.parse(JSON.parse(data));

      return this.userManagementService.updateUser(params.id, parsed, file);
    } catch (error: any) {
      throw handleError(error);
    }
  }
}
