import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { uploadToMinio } from './file.job';
import {
  CUpdateImageRepositoryBodyDto,
  CUpdateImageRepositoryParamsDto,
} from '../dto/update.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateImageRepository = async (
  prisma: PrismaService,
  params: CUpdateImageRepositoryParamsDto,
  body: CUpdateImageRepositoryBodyDto,
  file?: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.image_repository
      .findUniqueOrThrow({
        where: {
          id: params.id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
      })
      .catch(() => {
        throw handleError({
          message: 'image repository not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const data = body.data;

    let minioPathname: string | undefined = undefined;
    let minioFilehName: string | undefined = undefined;

    if (file) {
      const { filePathName, fileName } = await uploadToMinio(
        file!,
        data.image_name,
      );

      minioPathname = filePathName;
      minioFilehName = fileName;
    }

    const repository = await prisma.image_repository.update({
      where: { id: params.id },
      data: {
        image_name: minioFilehName,
        file_format: 'webp',
        link: minioPathname,
        file_size: file?.size,
        feature: data.feature,
        last_updated: new Date(),
        updated_by: null, // logged in username
      },
      select: { id: true },
    });

    await prisma.$transaction(async (tx) => {
      await tx.image_level_mapping.deleteMany({
        where: { image_repository_id: repository.id },
      });

      const levels = data.level_id.map((id) => ({
        image_repository_id: repository.id,
        levels_id: id,
      }));

      await tx.image_level_mapping.createMany({
        data: levels,
      });
    });

    await prisma.$transaction(async (tx) => {
      await tx.image_category_mapping.deleteMany({
        where: { image_repository_id: repository.id },
      });

      const categories = data.category_id.map((id) => ({
        image_repository_id: repository.id,
        category_id: id,
      }));

      await tx.image_category_mapping.createMany({
        data: categories,
      });
    });

    return {
      status: true,
      message: 'success update image repository',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
