import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const updateLearningCodeParamsSchema = z.object({
  id: z.coerce.number(),
});

export class CUpdateLearningCodeParamsDto extends createZodDto(
  updateLearningCodeParamsSchema,
) {}

const updateLearningCodeBodySchema = z.object({
  learning_code: z.string(),
  learning_code_name: z.string(),
  job_name_id: z.array(z.number()),
});

export class CUpdateLearningCodeBodyDto extends createZodDto(
  updateLearningCodeBodySchema,
) {}
