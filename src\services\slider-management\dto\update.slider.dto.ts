import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const zBooleanFromString = z.preprocess((val) => {
  if (val === 'true') return true;
  if (val === 'false') return false;
  return val;
}, z.boolean());

export const updateSliderSchema = z.object({
  data: z.preprocess(
    (val) => {
      if (typeof val === 'string') {
        try {
          return JSON.parse(val);
        } catch (e) {
          return val;
        }
      }
      return val;
    },
    z.object({
      slider_name: z.string().min(1, 'Slider Name cannot be empty'),
      link: z.string().optional(),
      is_deleted: zBooleanFromString.optional(),
    }),
  ),
});

const updateSliderParams = z.object({ id: z.coerce.number() });

export class CUpdateSliderBodyDto extends createZodDto(updateSliderSchema) {}

export class CUpdateSliderParamsDto extends createZodDto(updateSliderParams) {}
