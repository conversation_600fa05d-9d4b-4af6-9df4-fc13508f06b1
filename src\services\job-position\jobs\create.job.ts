import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { CCreateJobPositionDto } from '../dto/create.dto';

export const JCreateJobPosition = async (
  prisma: PrismaService,
  body: CCreateJobPositionDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.job_names.create({
      data: {
        job_name: body.job_name,
        level: body.level,
        department_name: body.department_name,
        level_name: body.level_name,
        job_function: body.job_function,
        job_id: body.job_id,
        ho_dept: body.ho_dept,
        entity_id: body.entity_id,
        job_position_type: body.job_position_type,
        is_need_neop: body.is_need_neop,
        is_need_welcoming_kit: body.is_need_welcoming_kit,
        starter_module_priority: body.starter_module_priority,
        job_function_id: body.job_function_id,
        level_id: body.level_id,
        is_active: body.is_active,
        department_id: body.department_id,
        is_deleted: false,
        created_by: '',
        updated_by: '',
        created_at: new Date(),
        last_updated: new Date(),
      },
    });

    return {
      status: true,
      message: 'success insert data job position',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
