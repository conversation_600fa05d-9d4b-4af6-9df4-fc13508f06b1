import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CUpdateCategoryDto,
  CUpdateCategoryParamsDto,
} from '../dto/update.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateCategory = async (
  prisma: PrismaService,
  params: CUpdateCategoryParamsDto,
  body: CUpdateCategoryDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.category
      .findUniqueOrThrow({
        where: {
          id: params.id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
      })
      .catch(() => {
        throw handleError({
          status: HttpStatus.NOT_FOUND,
          message: 'Category not found',
        });
      });

    await prisma.$transaction(async (tx) => {
      await tx.category.update({
        where: { id: params.id },
        data: {
          category_name: body.category_name,
          updated_by: null,
          last_updated: new Date(),
          is_deleted: body.is_deleted,
        },
      });

      const categories = await tx.category.findMany({
        where: {
          OR: [{ is_deleted: false }, { is_deleted: null }],
          category_name: body.category_name,
        },
      });

      if (categories.length > 1) {
        throw handleError({
          status: HttpStatus.CONFLICT,
          message: 'failed, category name must be unique',
        });
      }
    });

    return {
      status: true,
      message: 'success update data category',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
