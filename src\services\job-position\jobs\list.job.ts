import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetJobPositionListQueryDto,
  IGetJobPositionListResponseDto,
} from '../dto/list.dto';
import { Prisma } from '@prisma/client';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';

export const JGetJobPositionList = async (
  prisma: PrismaService,
  query: CGetJobPositionListQueryDto,
): Promise<IGlobalResponseDto<IGetJobPositionListResponseDto[]>> => {
  try {
    const { search, search_by, page = 1, limit = 10 } = query;

    const filter: Prisma.job_namesWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
      level: query.level,
      job_position_type: {
        contains: query.job_position_type,
        mode: 'insensitive',
      },
      is_need_neop: query.is_neop,
      starter_module_priority: {
        contains: query.starter_modul_priority,
        mode: 'insensitive',
      },
    };

    if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.job_names.findMany({
        where: filter,
        select: {
          ID: true,
          job_id: true,
          job_name: true,
          entity_id: true,
          department_id: true,
          department_name: true,
          job_function_id: true,
          job_function: true,
          level: true,
          job_position_type: true,
          is_need_neop: true,
          is_need_welcoming_kit: true,
          starter_module_priority: true,
          last_updated: true,
          updated_by: true,
          is_active: true,
          entity: true,
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
        skip,
        take,
      }),
      prisma.job_names.count({
        where: filter,
      }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetJobPositionListResponseDto[] = data.map((item) => ({
      id: item.ID,
      job_id: item.job_id,
      job_name: item.job_name,
      entity_id: item.entity_id,
      entity_name: item.entity?.entity_name ?? null,
      department_name: item.department_name,
      job_function: item.job_function,
      level: item.level,
      job_position_type: item.job_position_type,
      is_need_neop: item.is_need_neop,
      is_need_welcoming_kit: item.is_need_welcoming_kit,
      starter_module_priority: item.starter_module_priority,
      last_updated: item.last_updated?.toISOString() ?? null,
      updated_by: item.updated_by,
      is_active: item.is_active,
    }));

    return {
      status: true,
      message: 'success get data job position',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/job-list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
