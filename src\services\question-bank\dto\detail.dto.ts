import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getQuestionBankDetailParams = z.object({ id: z.coerce.number() });

export class CGetQuestionBankDetailParamsDto extends createZodDto(
  getQuestionBankDetailParams,
) {}

export interface IQuestionBankDetailResponseDto {
  id: number;
  question: string | null;
  type: string | null;
  option_a: string | null;
  option_b: string | null;
  option_c: string | null;
  option_d: string | null;
  correct_answer: string | null;
  correct_answer_percentage: number | null;
  feature: string | null;
  image_id: number | null;
  image_link: string | null;
  levels: {
    id: number | null;
    level: string | null;
  }[];
  categories: {
    id: number | null;
    name: string | null;
  }[];
  associated: {
    section_id: number | null;
    section_name: string | null;
  }[];
  created_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  last_updated: string | null;
}
