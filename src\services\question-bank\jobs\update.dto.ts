import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { JCreateImageRepository } from 'src/services/image-repository/jobs/create.job';
import {
  CUpdateQuestionBankBodyDto,
  CUpdateQuestionBankParamsDto,
} from '../dto/update.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateQuestionBank = async (
  prisma: PrismaService,
  params: CUpdateQuestionBankParamsDto,
  body: CUpdateQuestionBankBodyDto,
  file?: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.question
      .findUniqueOrThrow({
        where: {
          id: params.id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
      })
      .catch(() => {
        throw handleError({
          message: 'question bank not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const data = body.data;

    let imageId = data?.image_id;

    // insert into image repository
    if (file) {
      const imgRepository = await JCreateImageRepository(
        prisma,
        {
          data: {
            feature: data.feature,
            image_name: data.image_name,
            category_id: [],
            level_id: [],
          },
        },
        file,
        true,
      );

      if (imgRepository.data?.imageId) {
        imageId = imgRepository.data.imageId;
      }
    }

    const question = await prisma.question.update({
      where: { id: params.id },
      data: {
        question: data.question,
        type: data.type,
        a: data.option_a,
        b: data.option_b,
        c: data.option_c,
        d: data.option_d,
        correct_answer: data.correct_answer,
        correct_answer_percentage: null,
        feature: data.feature,
        image_id: imageId,
        is_deleted: data.is_deleted,
        last_updated: new Date(),
        updated_by: null, // logged in username
      },
      select: { id: true },
    });

    await prisma.$transaction(async (tx) => {
      await tx.question_level_mapping.deleteMany({
        where: { question_id: question.id },
      });

      const levels = data.level_id.map((id) => ({
        question_id: question.id,
        levels_id: id,
      }));

      await tx.question_level_mapping.createMany({
        data: levels,
      });
    });

    await prisma.$transaction(async (tx) => {
      await tx.question_category_mapping.deleteMany({
        where: { question_id: question.id },
      });

      const categories = data.category_id.map((id) => ({
        question_id: question.id,
        category_id: id,
      }));

      await tx.question_category_mapping.createMany({
        data: categories,
      });
    });

    return {
      status: true,
      message: 'success update question bank',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
