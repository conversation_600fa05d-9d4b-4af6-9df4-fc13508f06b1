import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { Prisma } from '@prisma/client';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import {
  CGetSubCategoryListQueryDto,
  IGetSubCategoryListResponseDto,
} from '../dto/list-subcategory.dto';
import dayjs from 'dayjs';

export const JGetSubCategoryList = async (
  prisma: PrismaService,
  query: CGetSubCategoryListQueryDto,
): Promise<IGlobalResponseDto<IGetSubCategoryListResponseDto[]>> => {
  try {
    const { search, page = 1, limit = 10 } = query;

    const filter: Prisma.sub_categoryWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    let searchBy: any = query.search_by;

    if (searchBy === 'sub_category_id') searchBy = 'id';

    if (search && searchBy) {
      if (searchBy === 'category_name') {
        filter.AND = [
          {
            category: {
              category_name: {
                contains: search as string,
                mode: 'insensitive',
              },
            },
          },
        ];
      } else if (searchBy === 'id') {
        filter.AND = [
          {
            id: +search,
          },
        ];
      } else if (searchBy === 'created_at') {
        filter.AND = [
          {
            created_at: {
              gte: dayjs(search).startOf('day').toISOString(),
              lte: dayjs(search).endOf('day').toISOString(),
            },
          },
        ];
      } else {
        filter.AND = [
          {
            [searchBy]: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.sub_category.findMany({
        where: filter,
        select: {
          id: true,
          sub_category_name: true,
          category: true,
          created_at: true,
          created_by: true,
          last_updated: true,
          updated_by: true,
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
        skip,
        take,
      }),
      prisma.sub_category.count({
        where: filter,
      }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetSubCategoryListResponseDto[] = data.map((item) => ({
      id: item.id,
      subcategory_name: item.sub_category_name,
      category_id: item.category?.id ?? null,
      category_name: item.category?.category_name ?? null,
      created_at: item.created_at?.toISOString() ?? null,
      created_by: item.created_by,
      last_updated: item.last_updated?.toISOString() ?? null,
      updated_by: item.updated_by,
    }));

    return {
      status: true,
      message: 'success get data subcategory',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/subcategory-list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
