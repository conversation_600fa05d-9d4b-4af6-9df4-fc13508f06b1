import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

const getManageMaterialListQuerySchema = z
  .object({
    type: z.enum(['video', 'document', 'audio']),
    category_id: z.coerce.number().optional(),
    level_id: z.coerce.number().optional(),
    feature: z.enum(['OnlineLearning', 'InClassTraining']).optional(),
    search: z.string().optional(),
    search_by: z
      .enum(['name', 'associated', 'created_by', 'updated_by'])
      .optional(),
  })
  .and(globalPaginationQuerySchema);

export class CGetManageMaterialListQueryDto extends createZodDto(
  getManageMaterialListQuerySchema,
) {}

export interface IGetManageMaterialListResponseDto {
  id: number;
  type: string | null;
  name: string | null;
  file_format: string | null;
  levels: {
    id: number | null;
    level: string | null;
  }[];
  categories: {
    id: number | null;
    name: string | null;
  }[];
  associated: {
    id: number | null;
    name: string | null;
  }[];
  link: string | null;
  filesize: number | null;
  feature: string | null;
  created_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  last_updated: string | null;
}
