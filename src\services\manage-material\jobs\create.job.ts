import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { CCreateMaterialBodyDto } from '../dto/create.dto';
import {
  getFileFormat,
  uploadToMinio,
  validateFiles,
} from './file-validation.job';

export const JCreateMaterial = async (
  prisma: PrismaService,
  body: CCreateMaterialBodyDto,
  file?: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await validateFiles(body.file_type, file);

    const data = body.data;

    const minioPathname = await uploadToMinio(body.file_type, file!);

    const repository = await prisma.materials_repository.create({
      data: {
        type: body.file_type,
        name: file?.originalname,
        file_format: getFileFormat(body.file_type),
        link: minioPathname,
        filesize: file?.size,
        is_deleted: false,
        feature: data.feature,
        created_at: new Date(),
        created_by: null, // logged in username
        last_updated: new Date(),
        updated_by: null, // logged in username
      },
      select: { id: true },
    });

    if (data.level_id?.length) {
      const levels = data.level_id.map((id) => ({
        materials_repository_id: repository.id,
        levels_id: id,
      }));

      await prisma.material_level_mapping.createMany({
        data: levels,
      });
    }

    if (data.category_id?.length) {
      const categories = data.category_id.map((id) => ({
        materials_repository_id: repository.id,
        category_id: id,
      }));

      await prisma.material_category_mapping.createMany({
        data: categories,
      });
    }

    return {
      status: true,
      message: 'success insert data material',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
