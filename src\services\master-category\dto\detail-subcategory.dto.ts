import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getSubCategoryDetailParams = z.object({ id: z.coerce.number() });

export class CGetSubCategoryDetailParamsDto extends createZodDto(
  getSubCategoryDetailParams,
) {}

export interface IGetSubCategoryDetailResponseDto {
  id: number;
  subcategory_name: string | null;
  category_id: number | null;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
