import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const getUserDownloadQuerySchema = z.object({
  search: z.string().optional(),
  search_by: z
    .enum([
      'npk',
      'name',
      'job_name',
      'email',
      'second_email',
      'phone_number',
      'updated_by',
    ])
    .optional(),
  type_id: z.coerce.number().optional(),
  role_id: z.coerce.number().optional(),
  entity_id: z.coerce.number().optional(),
  is_neop: z
    .string()
    .optional()
    .transform((val) => val === 'true'),
  is_new_user: z
    .string()
    .optional()
    .transform((val) => val === 'true'),
});

export class CGetUserDownloadQueryDto extends createZodDto(
  getUserDownloadQuerySchema,
) {}
