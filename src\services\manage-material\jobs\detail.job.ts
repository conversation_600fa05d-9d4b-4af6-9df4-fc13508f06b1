import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetManageMaterialDetailParamsDto,
  IManageMaterialDetailResponseDto,
} from '../dto/detail.dto';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { materials_repository } from '@prisma/client';

export const JGetManageMaterialDetail = async (
  prisma: PrismaService,
  params: CGetManageMaterialDetailParamsDto,
): Promise<IGlobalResponseDto<IManageMaterialDetailResponseDto>> => {
  try {
    const { id } = params;

    const data = await prisma.materials_repository
      .findFirstOrThrow({
        where: {
          id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        include: {
          material_level_mapping: {
            include: {
              levels: true,
            },
          },
          material_category_mapping: {
            include: {
              category: true,
            },
          },
          sub_section: {
            include: {
              sub_section_category_mapping: {
                include: {
                  category: true,
                },
              },
              sub_section_level_mapping: {
                include: {
                  levels: true,
                },
              },
            },
          },
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
      })
      .catch(() => {
        throw handleError({
          message: 'material not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IManageMaterialDetailResponseDto =
      mapMaterialDetailToDto(data);

    return {
      status: true,
      message: 'success get data material',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

export function mapMaterialDetailToDto(
  data: materials_repository & {
    material_level_mapping: {
      levels: { ID: number; name: string | null } | null;
    }[];
    material_category_mapping: {
      category: { id: number; category_name: string | null } | null;
    }[];
    sub_section: {
      ID: number;
      name: string | null;
      sub_section_category_mapping: {
        category: { id: number; category_name: string | null } | null;
      }[];
      sub_section_level_mapping: {
        levels: { ID: number; name: string | null } | null;
      }[];
    }[];
  },
): IManageMaterialDetailResponseDto {
  return {
    id: data.id,
    type: data.type,
    name: data.name,
    file_format: data.file_format,
    levels: data.material_level_mapping
      .filter((ml) => ml.levels)
      .map((ml) => ({
        id: ml.levels?.ID ?? null,
        level: ml.levels?.name ?? null,
      })),
    categories: data.material_category_mapping
      .filter((mc) => mc.category)
      .map((mc) => ({
        id: mc.category?.id ?? null,
        name: mc.category?.category_name ?? null,
      })),
    associated: data.sub_section.map((ss) => ({
      section_id: ss.ID ?? null,
      section_name: ss.name ?? null,
      categories: ss.sub_section_category_mapping
        .filter((sc) => sc.category)
        .map((sc) => ({
          section_category_id: sc.category?.id ?? null,
          section_category_name: sc.category?.category_name ?? null,
        })),
      levels: ss.sub_section_level_mapping
        .filter((sl) => sl.levels)
        .map((sl) => ({
          section_level_id: sl.levels?.ID ?? null,
          section_level_name: sl.levels?.name ?? null,
        })),
    })),
    link: data.link,
    filesize: data.filesize,
    feature: data.feature,
    created_at: data.created_at ? data.created_at.toISOString() : null,
    created_by: data.created_by,
    updated_by: data.updated_by,
    last_updated: data.last_updated ? data.last_updated.toISOString() : null,
  };
}
