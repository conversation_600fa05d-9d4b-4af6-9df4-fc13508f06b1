import { PrismaService } from 'src/services/prisma/prisma.service';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { handleError } from 'src/common/utils/error.util';
import { IGetSliderListResponseDto } from '../dto/list-slider.dto';

export const JGetSliderList = async (
  prisma: PrismaService,
): Promise<IGlobalResponseDto<IGetSliderListResponseDto[]>> => {
  try {
    const data = await prisma.sliders.findMany({
      where: {
        OR: [{ is_deleted: false }, { is_deleted: null }],
      },
      select: {
        ID: true,
        slider: true,
        slider_name: true,
        link: true,
      },
      orderBy: {
        ID: 'asc',
      },
    });

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetSliderListResponseDto[] = data.map((item) => ({
      id: item.ID,
      slider_desktop: item.slider,
      slider_name: item.slider_name,
      link: item.link,
    }));

    return {
      status: true,
      message: 'success get data slider',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
