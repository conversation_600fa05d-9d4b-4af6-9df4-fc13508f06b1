import { PrismaService } from 'src/services/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { handleError } from 'src/common/utils/error.util';
import {
  CGetImageRepositoryListQueryDto,
  IGetImageRepositoryListResponseDto,
} from '../dto/list.dto';

export const JGetImageRepositoryList = async (
  prisma: PrismaService,
  query: CGetImageRepositoryListQueryDto,
): Promise<IGlobalResponseDto<IGetImageRepositoryListResponseDto[]>> => {
  try {
    const {
      search,
      search_by,
      page = 1,
      limit = 10,
      category_id,
      feature,
      level_id,
    } = query;

    const filter: Prisma.image_repositoryWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
      feature,
      ...(category_id
        ? {
            image_category_mapping: {
              some: { category_id },
            },
          }
        : {}),
      ...(level_id
        ? {
            image_level_mapping: {
              some: { levels_id: level_id },
            },
          }
        : {}),
    };

    if (search && search_by && search_by === 'image_id') {
      filter.AND = [
        {
          id: +search,
        },
      ];
    } else if (search && search_by && search_by === 'question') {
      filter.AND = [
        {
          question: {
            some: {
              question: { contains: search as string, mode: 'insensitive' },
            },
          },
        },
      ];
    } else if (search && search_by) {
      filter.AND = [
        {
          [search_by]: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.image_repository.findMany({
        where: filter,
        include: {
          image_level_mapping: {
            include: {
              levels: true,
            },
          },
          image_category_mapping: {
            include: {
              category: true,
            },
          },
          question: true,
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
        skip,
        take,
      }),
      prisma.image_repository.count({ where: filter }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetImageRepositoryListResponseDto[] = data.map((item) => ({
      id: item.id,
      link: item.link,
      image_name: item.image_name,
      file_size: item.file_size,
      file_format: item.file_format,
      levels: item.image_level_mapping
        .filter((ml) => ml.levels)
        .map((ml) => ({
          id: ml.levels?.ID ?? null,
          name: ml.levels?.name ?? null,
        })),
      categories: item.image_category_mapping
        .filter((mc) => mc.category)
        .map((mc) => ({
          id: mc.category?.id ?? null,
          name: mc.category?.category_name ?? null,
        })),
      associated: item.question.map((q) => ({
        question_id: q.id ?? null,
        question: q.question ?? null,
      })),
      feature: item.feature,
      created_at: item.created_at ? item.created_at.toISOString() : null,
      created_by: item.created_by,
      updated_by: item.updated_by,
      last_updated: item.last_updated ? item.last_updated.toISOString() : null,
    }));

    return {
      status: true,
      message: 'success get data image repository',
      data: result,
      pagination: createPagination(
        page,
        limit,
        count,
        '/admin/learning/img-repository/list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
