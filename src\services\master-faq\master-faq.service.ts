import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CGetFAQListQueryDto } from './dto/list.dto';
import { JGetFAQList } from './jobs/list.job';
import { CGetFAQDetailParamsDto } from './dto/detail.dto';
import { JGetFAQDetail } from './jobs/detail.job';
import { CCreateFAQBodyDto } from './dto/create.dto';
import { JCreateFAQ } from './jobs/create.job';
import { JUpdateFAQ } from './jobs/update.job';
import { CUpdateFAQBodyDto, CUpdateFAQParamsDto } from './dto/update.dto';
import { CGetTagListQueryDto } from './dto/list-tag.dto';
import { JGetTagList } from './jobs/list-tag.job';

@Injectable()
export class MasterFAQService {
  constructor(private readonly prisma: PrismaService) {}

  getFAQList = (query: CGetFAQListQueryDto) => JGetFAQList(this.prisma, query);
  getFAQDetail = (params: CGetFAQDetailParamsDto) =>
    JGetFAQDetail(this.prisma, params);
  createFAQ = (body: CCreateFAQBodyDto, file: Express.Multer.File) =>
    JCreateFAQ(this.prisma, body, file);
  updateFAQ = (
    params: CUpdateFAQParamsDto,
    body: CUpdateFAQBodyDto,
    file: Express.Multer.File,
  ) => JUpdateFAQ(this.prisma, params, body, file);
  getTagList = (query: CGetTagListQueryDto) => JGetTagList(this.prisma, query);
}
