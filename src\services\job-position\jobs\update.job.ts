import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CUpdateJobPositionDto,
  CUpdateJobPositionParamsDto,
} from '../dto/update.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateJobPosition = async (
  prisma: PrismaService,
  params: CUpdateJobPositionParamsDto,
  body: CUpdateJobPositionDto,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.job_names
      .findUniqueOrThrow({
        where: {
          ID: params.id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
      })
      .catch(() => {
        throw handleError({
          status: HttpStatus.NOT_FOUND,
          message: 'Job not found',
        });
      });

    await prisma.job_names.update({
      where: { ID: params.id },
      data: {
        job_name: body.job_name,
        level: body.level,
        department_name: body.department_name,
        level_name: body.level_name,
        job_function: body.job_function,
        ho_dept: body.ho_dept,
        entity_id: body.entity_id,
        job_position_type: body.job_position_type,
        is_need_neop: body.is_need_neop,
        is_need_welcoming_kit: body.is_need_welcoming_kit,
        starter_module_priority: body.starter_module_priority,
        job_function_id: body.job_function_id,
        level_id: body.level_id,
        is_active: body.is_active,
        department_id: body.department_id,
        is_deleted: body.is_deleted,
        updated_by: '',
        last_updated: new Date(),
      },
    });

    return {
      status: true,
      message: 'success update data job position',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
