import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const getFAQDetailParams = z.object({ id: z.coerce.number() });

export class CGetFAQDetailParamsDto extends createZodDto(getFAQDetailParams) {}

export interface IGetFAQDetailResponseDto {
  id: number;
  question: string | null;
  answer: string | null;
  imgfaq: string | null;
  tag: {
    tag_id: number | null;
    tag_name: string | null;
  }[];
}
