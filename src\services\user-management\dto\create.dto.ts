import { createZodDto } from 'nestjs-zod';
import z from 'zod';

export const createUserBodySchema = z.object({
  npk: z.string(),
  email: z.string(),
  second_email: z.string().optional(),
  point: z.number().nullable(),
  forum_title: z.string(),
  name: z.string(),
  job_name: z.string(),
  password: z.string(),
  job_name_id: z.number(),
  phone_number: z.string(),
  user_type_id: z.number(),
  user_role_id: z.number(),
  supervisor_id: z.number().optional(),
  supervisor_name: z.string().optional(),
  supervisor_npk: z.string().optional(),
  is_active: z.boolean(),
  is_need_neop: z.boolean(),
  is_new_user: z.boolean(),
  is_deleted: z.boolean(),
  created_by: z.string(),
  updated_by: z.string(),
  forum_title_id: z.number(),
  entity_id: z.number(),
  is_need_welcoming_kit: z.boolean(),
});

export class CCreateUserBodyDto extends createZodDto(createUserBodySchema) {}
