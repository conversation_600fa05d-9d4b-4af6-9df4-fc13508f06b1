import { createZodDto } from 'nestjs-zod';
import { globalPaginationQuerySchema } from 'src/common/dto/pagination.dto';
import { z } from 'zod';

const getImageRepositoryListQuerySchema = z
  .object({
    category_id: z.coerce.number().optional(),
    level_id: z.coerce.number().optional(),
    feature: z.enum(['OnlineLearning', 'InClassTraining']).optional(),
    search: z.union([z.string(), z.number()]).optional(),
    search_by: z
      .enum([
        'image_id',
        'image_name',
        'file_format',
        'question',
        'created_by',
        'updated_by',
      ])
      .optional(),
  })
  .refine(
    (data) => {
      if (data.search_by === 'image_id') {
        return (
          data.search === undefined ||
          typeof data.search === 'number' ||
          !isNaN(Number(data.search))
        );
      }
      return true;
    },
    {
      message: 'search must be number if search_by=question_id',
      path: ['search'],
    },
  )
  .transform((data) => {
    if (data.search_by === 'image_id' && typeof data.search === 'string') {
      return { ...data, search: Number(data.search) };
    }
    return data;
  })
  .and(globalPaginationQuerySchema);

export class CGetImageRepositoryListQueryDto extends createZodDto(
  getImageRepositoryListQuerySchema,
) {}

export interface IGetImageRepositoryListResponseDto {
  id: number;
  image_name: string | null;
  file_format: string | null;
  file_size: number | null;
  link: string | null;
  categories: { id: number | null; name: string | null }[];
  levels: { id: number | null; name: string | null }[];
  associated: { question_id: number | null; question: string | null }[];
  feature: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
