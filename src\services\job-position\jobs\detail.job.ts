import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetJobPositionDetailParamsDto,
  IGetJobPositionDetailResponseDto,
} from '../dto/detail.dto';

export const JGetJobPositionDetail = async (
  prisma: PrismaService,
  params: CGetJobPositionDetailParamsDto,
): Promise<IGlobalResponseDto<IGetJobPositionDetailResponseDto>> => {
  try {
    const { id } = params;

    const data = await prisma.job_names
      .findUniqueOrThrow({
        where: {
          ID: id,
          is_active: true,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        select: {
          ID: true,
          job_id: true,
          job_name: true,
          entity_id: true,
          department_id: true,
          department_name: true,
          job_function_id: true,
          job_function: true,
          level: true,
          level_id: true,
          job_position_type: true,
          is_need_neop: true,
          is_need_welcoming_kit: true,
          starter_module_priority: true,
          last_updated: true,
          updated_by: true,
          is_active: true,
          entity: true,
        },
      })
      .catch(() => {
        throw handleError({
          message: 'Job not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IGetJobPositionDetailResponseDto = {
      id: data.ID,
      job_id: data.job_id,
      job_name: data.job_name,
      entity_id: data.entity_id,
      entity_name: data.entity?.entity_name ?? null,
      department_id: data.department_id,
      department_name: data.department_name,
      job_function_id: data.job_function_id,
      job_function: data.job_function,
      level: data.level,
      level_id: data.level_id,
      job_position_type: data.job_position_type,
      is_need_neop: data.is_need_neop,
      is_need_welcoming_kit: data.is_need_welcoming_kit,
      starter_module_priority: data.starter_module_priority,
      last_updated: data.last_updated?.toISOString() ?? null,
      updated_by: data.updated_by,
      is_active: data.is_active,
    };

    return {
      status: true,
      message: 'success get data job position',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
