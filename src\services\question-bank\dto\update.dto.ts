import { createZodDto } from 'nestjs-zod';
import { zBooleanOptional } from 'src/common/utils/validation.util';
import z from 'zod';

export const updateQuestionBankSchema = z.object({
  data: z.preprocess(
    (val) => {
      if (typeof val === 'string') {
        try {
          return JSON.parse(val);
        } catch {
          return val;
        }
      }
      return val;
    },
    z.object({
      question: z.string().min(1, 'question cannot be empty'),
      type: z.string().min(1, 'type cannot be empty'),
      feature: z.enum(['OnlineLearning', 'InClassTraining']),
      option_a: z.string().optional(),
      option_b: z.string().optional(),
      option_c: z.string().optional(),
      option_d: z.string().optional(),
      correct_answer: z.string().optional(),
      image_name: z.string().optional(),
      image_id: z.coerce.number().optional(),
      is_deleted: zBooleanOptional(),
      category_id: z
        .any() // string/array
        .optional()
        .transform((val) => {
          if (!val) return [];

          // "[1,2,3]"
          if (typeof val === 'string') {
            try {
              const parsed = JSON.parse(val);
              if (Array.isArray(parsed)) return parsed.map(Number);
            } catch {
              // "1,2,3"
              return val.split(',').map((v) => Number(v.trim()));
            }
          }

          // ["1","2","3"]
          if (Array.isArray(val)) {
            return val.map((v) => Number(v));
          }

          return [];
        })
        .refine((arr) => arr.every((n) => typeof n === 'number' && !isNaN(n)), {
          message: 'category id must be numbers',
        }),
      level_id: z
        .any() // string/array
        .optional()
        .transform((val) => {
          if (!val) return [];

          // "[1,2,3]"
          if (typeof val === 'string') {
            try {
              const parsed = JSON.parse(val);
              if (Array.isArray(parsed)) return parsed.map(Number);
            } catch {
              // "1,2,3"
              return val.split(',').map((v) => Number(v.trim()));
            }
          }

          // ["1","2","3"]
          if (Array.isArray(val)) {
            return val.map((v) => Number(v));
          }

          return [];
        })
        .refine((arr) => arr.every((n) => typeof n === 'number' && !isNaN(n)), {
          message: 'level id must be numbers',
        }),
    }),
  ),
});

const updateQuestionBankParams = z.object({ id: z.coerce.number() });

export class CUpdateQuestionBankBodyDto extends createZodDto(
  updateQuestionBankSchema,
) {}

export class CUpdateQuestionBankParamsDto extends createZodDto(
  updateQuestionBankParams,
) {}
