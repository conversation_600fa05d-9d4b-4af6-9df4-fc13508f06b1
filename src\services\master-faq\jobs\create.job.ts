import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { CCreateFAQBodyDto } from '../dto/create.dto';
import {
  convertImageToWebp,
  removeExtension,
} from 'src/common/utils/image-processor.util';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import dayjs from 'dayjs';

export const JCreateFAQ = async (
  prisma: PrismaService,
  body: CCreateFAQBodyDto,
  file: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    const data = body.data;

    const faqTags = data.tags.map((it: any) => ({ tags_id: +it }));

    const faq = await prisma.faq.create({
      data: {
        question: data.question,
        answer: data.answer,
        faq_tags: { createMany: { data: faqTags } },
      },
      select: { ID: true },
    });

    if (file && file.mimetype.startsWith('image/')) {
      const pathName = await uploadFAQImage(file);

      await prisma.faq.update({
        where: { ID: faq.ID },
        data: {
          imgFaq: pathName,
        },
      });
    }

    return {
      status: true,
      message: 'success insert data',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};

async function uploadFAQImage(file: Express.Multer.File) {
  let buffer = file.buffer;
  let filePathName = file.originalname;
  const mimetype = 'image/webp';

  buffer = await convertImageToWebp(buffer);
  filePathName = `faq/${dayjs().unix()}-${removeExtension(filePathName)}.webp`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': mimetype,
  });

  return `lemon/${filePathName}`;
}
