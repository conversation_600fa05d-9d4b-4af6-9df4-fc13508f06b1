import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetFAQDetailParamsDto,
  IGetFAQDetailResponseDto,
} from '../dto/detail.dto';
import { handleError } from 'src/common/utils/error.util';
import { HttpStatus } from '@nestjs/common';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';

export const JGetFAQDetail = async (
  prisma: PrismaService,
  params: CGetFAQDetailParamsDto,
): Promise<IGlobalResponseDto<IGetFAQDetailResponseDto>> => {
  try {
    const { id } = params;

    const faqDetail = await prisma.faq
      .findUniqueOrThrow({
        where: {
          ID: id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
        select: {
          ID: true,
          question: true,
          answer: true,
          imgFaq: true,
          faq_tags: {
            include: { tags: true },
          },
        },
      })
      .catch(() => {
        throw handleError({
          message: 'FAQ not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    const result: IGetFAQDetailResponseDto = {
      id: faqDetail.ID,
      question: faqDetail.question,
      answer: faqDetail.answer,
      imgfaq: faqDetail.imgFaq,
      tag: faqDetail.faq_tags.map((it) => ({
        tag_id: it.tags_id,
        tag_name: it.tags?.name ?? null,
      })),
    };

    return {
      status: true,
      message: 'success get data faq',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
