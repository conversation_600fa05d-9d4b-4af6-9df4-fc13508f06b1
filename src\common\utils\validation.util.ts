import { z } from 'zod';

export const zBoolean = () =>
  z
    .union([z.enum(['true', 'false']), z.boolean()])
    .transform((v) =>
      v === undefined ? undefined : v === 'true' || v === true,
    );

export const zBooleanOptional = () =>
  z
    .union([z.enum(['true', 'false']), z.boolean()])
    .optional()
    .transform((v) =>
      v === undefined ? undefined : v === 'true' || v === true,
    );
