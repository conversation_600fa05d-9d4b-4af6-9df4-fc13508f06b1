import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetDepartmentListQueryDto,
  IGetDepartmentListResponseDto,
} from '../dto/list-department.dto';

export const JGetDepartmentList = async (
  prisma: PrismaService,
  query: CGetDepartmentListQueryDto,
): Promise<IGlobalResponseDto<IGetDepartmentListResponseDto[]>> => {
  try {
    const data = await prisma.departments.findMany({
      where: {
        department_name: { contains: query.search, mode: 'insensitive' },
      },
      select: {
        department_id: true,
        department_name: true,
        branch_grade: true,
      },
      orderBy: {
        department_name: 'asc',
      },
    });

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetDepartmentListResponseDto[] = data.map((item) => ({
      id: item.department_id,
      department_name: item.department_name,
      branch_grade: item.branch_grade,
    }));

    return {
      status: true,
      message: 'success get data department',
      data: result,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
