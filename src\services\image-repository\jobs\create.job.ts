import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { uploadToMinio, validateFile } from './file.job';
import { CCreateImageRepositoryBodyDto } from '../dto/create.dto';

export const JCreateImageRepository = async (
  prisma: PrismaService,
  body: CCreateImageRepositoryBodyDto,
  file?: Express.Multer.File,
  returnImageId = false,
): Promise<IGlobalResponseDto<{ imageId: number } | null>> => {
  try {
    await validateFile(file);

    const data = body.data;

    const { filePathName, fileName } = await uploadToMinio(
      file!,
      data.image_name,
    );

    const repository = await prisma.image_repository.create({
      data: {
        image_name: fileName,
        file_format: 'webp',
        link: filePathName,
        file_size: file?.size,
        is_deleted: false,
        feature: data.feature,
        created_at: new Date(),
        created_by: null, // logged in username
        last_updated: new Date(),
        updated_by: null, // logged in username
      },
      select: { id: true },
    });

    if (data.level_id?.length) {
      const levels = data.level_id.map((id) => ({
        image_repository_id: repository.id,
        levels_id: id,
      }));

      await prisma.image_level_mapping.createMany({
        data: levels,
      });
    }

    if (data.category_id?.length) {
      const categories = data.category_id.map((id) => ({
        image_repository_id: repository.id,
        category_id: id,
      }));

      await prisma.image_category_mapping.createMany({
        data: categories,
      });
    }

    return {
      status: true,
      message: 'success insert image repository',
      data: returnImageId ? { imageId: repository.id } : null,
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
