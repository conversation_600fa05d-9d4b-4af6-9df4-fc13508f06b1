import { handleError } from 'src/common/utils/error.util';
import { TFileType } from '../dto/create.dto';
import { uploadFileToMinio } from 'src/common/utils/minio.util';
import dayjs from 'dayjs';

const MAX_FILE_SIZE_VIDEO = 120 * 1024 * 1024; // 120 MB
const MAX_FILE_SIZE_AUDIO = 25 * 1024 * 1024; // 25 MB
const MAX_FILE_SIZE_DOC = 30 * 1024 * 1024; // 30 MB

export const allowedMimeTypes = {
  video: 'video/mp4',
  audio: 'audio/mpeg',
  document: 'application/pdf',
};

export const getFileType = (mimeType: string) => {
  if (mimeType === allowedMimeTypes.video) return 'video';
  if (mimeType === allowedMimeTypes.audio) return 'audio';
  if (mimeType === allowedMimeTypes.document) return 'document';
  return null;
};

export const getMaxSizeForFileType = (fileType: TFileType) => {
  if (fileType === 'video') return MAX_FILE_SIZE_VIDEO;
  if (fileType === 'audio') return MAX_FILE_SIZE_AUDIO;
  if (fileType === 'document') return MAX_FILE_SIZE_DOC;
  return 0;
};

export const getFileFormat = (fileType: TFileType) => {
  if (fileType === 'video') return 'MP4';
  if (fileType === 'audio') return 'MP3';
  if (fileType === 'document') return 'PDF';
  return null;
};

export async function validateFiles(
  allowedFileType: TFileType,
  file?: Express.Multer.File,
) {
  if (!file) {
    throw handleError({
      status: 400,
      message: 'file is required',
    });
  }

  const fileType = getFileType(file.mimetype);

  if (!fileType) {
    throw handleError({
      status: 400,
      message: 'file type is not valid',
    });
  }

  if (fileType !== allowedFileType) {
    throw handleError({
      status: 400,
      message: `file "${file.originalname}" is not allowed with ${allowedFileType} type`,
    });
  }

  // Validasi ukuran file berdasarkan tipe
  const maxFileSize = getMaxSizeForFileType(fileType);
  if (file.size > maxFileSize) {
    throw handleError({
      status: 400,
      message: `file size exceeds the limit for ${fileType}. Max size: ${maxFileSize / (1024 * 1024)} MB`,
    });
  }
}

export async function uploadToMinio(
  type: TFileType,
  file: Express.Multer.File,
) {
  let buffer = file.buffer;
  let filePathName = `materials/${type}/${dayjs().unix()}-${file.originalname}`;

  await uploadFileToMinio(filePathName, buffer, {
    'Content-Type': file.mimetype,
  });

  return `lemon/${filePathName}`;
}
