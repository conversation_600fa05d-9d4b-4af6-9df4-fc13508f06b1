import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { JGetSliderList } from './jobs/list-slider.job';
import { JGetSliderDetail } from './jobs/detail-slider.job';
import { CGetSliderDetailParamsDto } from './dto/detail-slider.dto';
import { CCreateSliderBodyDto, ISliderImage } from './dto/create-slider.dto';
import { JCreateSlider } from './jobs/create-slider.job';
import { JUpdateSlider } from './jobs/update-slider.job';
import {
  CUpdateSliderBodyDto,
  CUpdateSliderParamsDto,
} from './dto/update.slider.dto';

@Injectable()
export class SliderManagementService {
  constructor(private readonly prisma: PrismaService) {}

  getSliderList = () => JGetSliderList(this.prisma);
  getSliderDetail = (params: CGetSliderDetailParamsDto) =>
    JGetSliderDetail(this.prisma, params);
  createSlider = (body: CCreateSliderBodyDto, file: ISliderImage) =>
    JCreateSlider(this.prisma, body, file);
  updateSlider = (
    params: CUpdateSliderParamsDto,
    body: CUpdateSliderBodyDto,
    file: ISliderImage,
  ) => JUpdateSlider(this.prisma, params, body, file);
}
