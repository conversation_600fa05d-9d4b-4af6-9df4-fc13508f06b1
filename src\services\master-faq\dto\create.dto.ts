import { createZodDto } from 'nestjs-zod';
import z from 'zod';

export const createFAQSchema = z.object({
  data: z.preprocess(
    (val) => {
      if (typeof val === 'string') {
        try {
          return JSON.parse(val);
        } catch {
          return val;
        }
      }
      return val;
    },
    z.object({
      question: z.string().min(1, 'Question cannot be empty'),
      answer: z.string().min(1, 'Answer cannot be empty'),
      imgfaq: z.string().optional(),
      tags: z
        .any() // string/array
        .optional()
        .transform((val) => {
          if (!val) return [];

          // "[1,2,3]"
          if (typeof val === 'string') {
            try {
              const parsed = JSON.parse(val);
              if (Array.isArray(parsed)) return parsed.map(Number);
            } catch {
              // "1,2,3"
              return val.split(',').map((v) => Number(v.trim()));
            }
          }

          // ["1","2","3"]
          if (Array.isArray(val)) {
            return val.map((v) => Number(v));
          }

          return [];
        })
        .refine((arr) => arr.every((n) => typeof n === 'number' && !isNaN(n)), {
          message: 'Tags must be numbers',
        }),
    }),
  ),
});

export class CCreateFAQBodyDto extends createZodDto(createFAQSchema) {}
