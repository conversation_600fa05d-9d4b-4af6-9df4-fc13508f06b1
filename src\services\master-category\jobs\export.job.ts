import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { Prisma } from '@prisma/client';
import { CExportCategoryQueryDto } from '../dto/export.dto';
import { Workbook } from 'exceljs';
import dayjs from 'dayjs';

interface ICategoryData {
  id: number;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}

export const JExportCategoryList = async (
  prisma: PrismaService,
  query: CExportCategoryQueryDto,
) => {
  try {
    const { search } = query;

    const filter: Prisma.categoryWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    let searchBy: any = query.search_by;

    if (searchBy === 'category_id') searchBy = 'id';

    if (search && searchBy) {
      if (searchBy === 'id') {
        filter.AND = [
          {
            id: +search,
          },
        ];
      } else if (searchBy === 'created_at') {
        filter.AND = [
          {
            created_at: {
              gte: dayjs(search).startOf('day').toISOString(),
              lte: dayjs(search).endOf('day').toISOString(),
            },
          },
        ];
      } else {
        filter.AND = [
          {
            [searchBy]: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    const data = await prisma.category.findMany({
      where: filter,
      select: {
        id: true,
        category_name: true,
        created_at: true,
        created_by: true,
        last_updated: true,
        updated_by: true,
      },
      orderBy: { id: 'asc' },
    });

    const result: ICategoryData[] = data.map((it) => {
      return {
        ...it,
        created_at: it?.created_at
          ? dayjs(it.created_at).format('DD/MM/YYYY')
          : null,
        last_updated: it?.last_updated
          ? dayjs(it.last_updated).format('DD/MM/YYYY')
          : null,
      };
    });

    return await createFile(result);
  } catch (error: any) {
    throw handleError(error);
  }
};

const createFile = async (data: ICategoryData[]) => {
  const workbook = new Workbook();

  const ws = workbook.addWorksheet();

  // 1) Definisikan columns (pakai header & key seperti semula)
  const columns = [
    { header: 'Category ID', key: 'id' },
    { header: 'Category Name', key: 'category_name' },
    { header: 'Created At', key: 'created_at' },
    { header: 'Created By', key: 'created_by' },
    { header: 'Last Updated', key: 'last_updated' },
    { header: 'Updated By', key: 'updated_by' },
  ];

  ws.columns = columns;

  // 2) Kosongkan row-1 (exceljs menulis header di sini secara default)
  ws.getRow(1).values = new Array(columns.length + 1).fill(null);

  const titleCell = ws.getCell(1, 1);

  titleCell.alignment = { horizontal: 'center' };
  titleCell.font = { bold: true };

  // 3) Row 2: Header manual (bold)
  const headerRow = ws.getRow(2);
  columns.forEach((c, idx) => {
    const cell = headerRow.getCell(idx + 1);
    cell.value = c.header;
    cell.font = { bold: true };
    cell.alignment = { horizontal: 'center' };
  });
  headerRow.commit();

  // 4) Data mulai baris 3
  data.forEach((val) => ws.addRow(val));

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
};
