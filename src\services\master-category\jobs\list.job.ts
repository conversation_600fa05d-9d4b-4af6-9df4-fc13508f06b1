import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  CGetCategoryListQueryDto,
  IGetCategoryListResponseDto,
} from '../dto/list.dto';
import { Prisma } from '@prisma/client';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import dayjs from 'dayjs';

export const JGetCategoryList = async (
  prisma: PrismaService,
  query: CGetCategoryListQueryDto,
): Promise<IGlobalResponseDto<IGetCategoryListResponseDto[]>> => {
  try {
    const { search, page = 1, limit } = query;

    const filter: Prisma.categoryWhereInput = {
      OR: [{ is_deleted: false }, { is_deleted: null }],
    };

    let searchBy: any = query.search_by;

    if (searchBy === 'category_id') searchBy = 'id';

    if (search && searchBy) {
      if (searchBy === 'id') {
        filter.AND = [
          {
            id: +search,
          },
        ];
      } else if (searchBy === 'created_at') {
        filter.AND = [
          {
            created_at: {
              gte: dayjs(search).startOf('day').toISOString(),
              lte: dayjs(search).endOf('day').toISOString(),
            },
          },
        ];
      } else {
        filter.AND = [
          {
            [searchBy]: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    const { skip, take } = getOffset(page, limit);

    const [data, count] = await Promise.all([
      prisma.category.findMany({
        where: filter,
        select: {
          id: true,
          category_name: true,
          created_at: true,
          created_by: true,
          last_updated: true,
          updated_by: true,
        },
        orderBy: {
          last_updated: {
            sort: 'desc',
            nulls: 'last',
          },
        },
        skip,
        take,
      }),
      prisma.category.count({
        where: filter,
      }),
    ]);

    if (data.length === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: [],
      };
    }

    const result: IGetCategoryListResponseDto[] = data.map((item) => ({
      id: item.id,
      category_name: item.category_name,
      created_at: item.created_at?.toISOString() ?? null,
      created_by: item.created_by,
      last_updated: item.last_updated?.toISOString() ?? null,
      updated_by: item.updated_by,
    }));

    return {
      status: true,
      message: 'success get data category',
      data: result,
      pagination: createPagination(
        page,
        limit ?? 0,
        count,
        '/admin/category-list',
        query,
      ),
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
