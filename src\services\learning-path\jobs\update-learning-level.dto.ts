import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { HttpStatus } from '@nestjs/common';
import {
  CUpdateLearningLevelBodyDto,
  CUpdateLearningLevelParamsDto,
} from '../dto/update-learning-level.dto';

export const JUpdateLearningLevel = async (
  prisma: PrismaService,
  params: CUpdateLearningLevelParamsDto,
  body: CUpdateLearningLevelBodyDto,
): Promise<IGlobalResponseDto> => {
  try {
    const { level, name, status, is_deleted } = body;
    const { id } = params;

    const isExist = await prisma.levels.findFirst({
      where: {
        is_deleted: false,
        OR: [
          {
            level: level,
          },
          {
            name: name,
          },
        ],
        NOT: {
          ID: id,
        },
      },
    });

    if (isExist) {
      const isLevelExist = isExist.level === level;

      throw handleError({
        status: HttpStatus.BAD_REQUEST,
        message: `data ${isLevelExist ? 'level' : 'name'} already exists`,
      });
    }

    await prisma.levels.update({
      where: {
        ID: params.id,
      },
      data: {
        level: level,
        name: name,
        status: status,
        is_deleted: is_deleted,
      },
    });

    return {
      status: true,
      message: 'success update learning level',
      data: null,
    };
  } catch (error) {
    throw handleError(error);
  }
};
