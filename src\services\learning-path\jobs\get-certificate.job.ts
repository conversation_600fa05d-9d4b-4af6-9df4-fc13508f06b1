import { handleError } from 'src/common/utils/error.util';
import { PrismaService } from 'src/services/prisma/prisma.service';
import {
  CGetCertificateListQueryDto,
  IGetCertificateListResponseDto,
} from '../dto/get-certificate.dto';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import { createPagination, getOffset } from 'src/common/utils/pagination.util';
import { Prisma } from '@prisma/client';
import dayjs from 'dayjs';

export const JGetCertificateList = async (
  prisma: PrismaService,
  query: CGetCertificateListQueryDto,
): Promise<IGlobalResponseDto<IGetCertificateListResponseDto[] | null>> => {
  try {
    const {
      limit,
      page,
      search,
      search_by,
      module_type,
      status,
      issue_date_start,
      issue_date_end,
      expired_date_start,
      expired_date_end,
    } = query;

    const { skip, take } = getOffset(page, limit);

    const filter: Prisma.modul_certificateWhereInput = {};

    filter.AND = [];

    // Search functionality
    if (search && search_by) {
      if (search_by === 'npk') {
        filter.AND.push({
          user_details: {
            npk: {
              contains: search,
              mode: 'insensitive',
            },
          },
        });
      }

      if (search_by === 'user_id') {
        filter.AND.push({
          user_id: parseInt(search) || undefined,
        });
      }

      if (search_by === 'name') {
        filter.AND.push({
          user_details: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        });
      }

      if (search_by === 'module_name') {
        filter.AND.push({
          moduls: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        });
      }

      if (search_by === 'level') {
        filter.AND.push({
          moduls: {
            levels: {
              level: parseInt(search) || undefined,
            },
          },
        });
      }

      if (search_by === 'attempt') {
        filter.AND.push({
          modul_assignment: {
            attempt: parseInt(search) || undefined,
          },
        });
      }
    }

    // Filter by module_type (multiple values)
    if (module_type && module_type.length > 0) {
      filter.AND.push({
        moduls: {
          type: {
            in: module_type,
          },
        },
      });
    }

    // Filter by status
    if (status) {
      filter.AND.push({
        status: status,
      });
    }

    // Filter by issue_date range
    if (issue_date_start || issue_date_end) {
      const dateFilter: any = {};
      if (issue_date_start) {
        dateFilter.gte = new Date(issue_date_start);
      }
      if (issue_date_end) {
        dateFilter.lte = new Date(issue_date_end);
      }
      filter.AND.push({
        issued_date: dateFilter,
      });
    }

    // Filter by expired_date range
    if (expired_date_start || expired_date_end) {
      const dateFilter: any = {};
      if (expired_date_start) {
        dateFilter.gte = new Date(expired_date_start);
      }
      if (expired_date_end) {
        dateFilter.lte = new Date(expired_date_end);
      }
      filter.AND.push({
        expired_date: dateFilter,
      });
    }

    const [data, total] = await Promise.all([
      prisma.modul_certificate.findMany({
        where: filter,
        select: {
          id: true,
          user_id: true,
          module_id: true,
          modul_assignment_id: true,
          issued_date: true,
          expired_date: true,
          status: true,
          user_details: {
            select: {
              npk: true,
              name: true,
            },
          },
          moduls: {
            select: {
              name: true,
              type: true,
              level_id: true,
              levels: {
                select: {
                  level: true,
                },
              },
            },
          },
          modul_assignment: {
            select: {
              attempt: true,
            },
          },
        },
        skip,
        take,
        orderBy: {
          issued_date: 'desc',
        },
      }),
      prisma.modul_certificate.count({
        where: filter,
      }),
    ]);

    if (total === 0) {
      return {
        status: true,
        message: 'no data exist',
        data: null,
      };
    }

    if (!data.length) {
      return {
        status: true,
        message: 'data not found',
        data: null,
      };
    }

    const response: IGetCertificateListResponseDto[] = data.map((item) => ({
      id: item.id,
      user_id: item.user_id,
      npk: item.user_details?.npk || null,
      name: item.user_details?.name || null,
      module_id: item.module_id,
      module_name: item.moduls?.name || null,
      module_type: item.moduls?.type || null,
      level_id: item.moduls?.level_id || null,
      level: item.moduls?.levels?.level || null,
      module_assignment_id: item.modul_assignment_id,
      attempt: item.modul_assignment?.attempt?.toString() || null,
      issued_date: item.issued_date
        ? dayjs(item.issued_date).toISOString()
        : null,
      expired_date: item.expired_date
        ? dayjs(item.expired_date).toISOString()
        : null,
      status: item.status,
    }));

    return {
      status: true,
      message: 'success get data certificate',
      data: response,
      pagination: createPagination(
        page,
        limit,
        total,
        '/admin/learning/certificate/list',
        query,
      ),
    };
  } catch (error) {
    throw handleError(error);
  }
};
