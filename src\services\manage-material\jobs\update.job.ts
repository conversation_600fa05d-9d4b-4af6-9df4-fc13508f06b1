import { PrismaService } from 'src/services/prisma/prisma.service';
import { handleError } from 'src/common/utils/error.util';
import { IGlobalResponseDto } from 'src/common/dto/response.dto';
import {
  getFileFormat,
  uploadToMinio,
  validateFiles,
} from './file-validation.job';
import {
  CUpdateMaterialBodyDto,
  CUpdateMaterialParamsDto,
} from '../dto/update.dto';
import { HttpStatus } from '@nestjs/common';

export const JUpdateMaterial = async (
  prisma: PrismaService,
  params: CUpdateMaterialParamsDto,
  body: CUpdateMaterialBodyDto,
  file?: Express.Multer.File,
): Promise<IGlobalResponseDto<any>> => {
  try {
    await prisma.materials_repository
      .findUniqueOrThrow({
        where: {
          id: params.id,
          OR: [{ is_deleted: false }, { is_deleted: null }],
        },
      })
      .catch(() => {
        throw handleError({
          message: 'material not found',
          status: HttpStatus.NOT_FOUND,
        });
      });

    // file is nullable so check only if exist
    if (file) {
      await validateFiles(body.file_type, file);
    }

    const data = body.data;

    const link = file ? await uploadToMinio(body.file_type, file) : null;

    const updateData: {
      type: string;
      feature: string;
      last_updated: Date;
      updated_by: null;
      name?: string;
      file_format?: string | null;
      link?: string | null;
      filesize?: number | null;
    } = {
      type: body.file_type,
      feature: data.feature,
      last_updated: new Date(),
      updated_by: null, // logged in username
    };

    // Only include file-related fields if file exists
    if (file) {
      updateData.name = file.originalname;
      updateData.file_format = getFileFormat(body.file_type);
      updateData.link = link;
      updateData.filesize = file.size;
    }

    if (data.file_name) {
      updateData.name = data.file_name;
    }

    const repository = await prisma.materials_repository.update({
      where: { id: params.id },
      data: updateData,
      select: { id: true },
    });

    await prisma.$transaction(async (tx) => {
      await tx.material_level_mapping.deleteMany({
        where: { materials_repository_id: repository.id },
      });

      const levels = data.level_id.map((id) => ({
        materials_repository_id: repository.id,
        levels_id: id,
      }));

      await tx.material_level_mapping.createMany({
        data: levels,
      });
    });

    await prisma.$transaction(async (tx) => {
      await tx.material_category_mapping.deleteMany({
        where: { materials_repository_id: repository.id },
      });

      const categories = data.category_id.map((id) => ({
        materials_repository_id: repository.id,
        category_id: id,
      }));

      await tx.material_category_mapping.createMany({
        data: categories,
      });
    });

    return {
      status: true,
      message: 'success update data material',
      data: {},
    };
  } catch (error: any) {
    throw handleError(error);
  }
};
